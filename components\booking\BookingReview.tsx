import { Card } from "@/components/ui/card";
import {
    Calendar,
    Check,
    CreditCard,
    MapPin,
    Plane,
    User,
} from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import {
    capitalizeFirstLetter,
    formatDate,
    formatDuration,
    formatTime,
} from "@/utils/dateFormat";
import React from "react";
import { ScrollView, Text, View } from "react-native";

export default function BookingReview() {
    const { bookingForm } = useBookingStore();

    // Calculate total price
    const calculateTotal = () => {
        return bookingForm.paymentInfo.totalAmount || 0;
    };

    return (
        <ScrollView className="flex-1">
            <View className="p-4 gap-4">
                <Text className="text-2xl font-bold mb-4 text-center">
                    Booking Review
                </Text>

                {/* Flight Summary */}
                <Card className="mb-4 rounded-xl overflow-hidden">
                    <View className="bg-blue-600 p-4 flex-row items-center gap-3">
                        <View className="bg-blue-500/20 rounded-full w-10 h-10 items-center justify-center">
                            <Plane size={20} color="white" />
                        </View>
                        <Text className="text-white text-lg font-bold">
                            Flight Details
                        </Text>
                    </View>

                    <View className="p-4">
                        {bookingForm.flightInfo.flights.map(
                            (flight, flightIndex) => (
                                <View key={flightIndex} className="mb-4">
                                    <View className="flex-row justify-between mb-3">
                                        <Text className="text-base font-semibold">
                                            Flight {flight.flight.flightNumber}
                                        </Text>
                                        <Text className="text-sm text-gray-500">
                                            {flight.class.cabinClass} Class
                                        </Text>
                                    </View>

                                    {flight.flight.segments.map(
                                        (segment, segmentIndex) => (
                                            <View
                                                key={segmentIndex}
                                                className="mb-4"
                                            >
                                                <View className="flex-row items-center justify-between">
                                                    <View className="items-center w-24">
                                                        <Text className="text-lg font-bold text-blue-600">
                                                            {formatTime(
                                                                segment.departureDate
                                                            )}
                                                        </Text>
                                                        <Text className="text-base font-medium my-1">
                                                            {capitalizeFirstLetter(
                                                                segment.from
                                                            )}
                                                        </Text>
                                                        <Text className="text-xs text-gray-500">
                                                            {formatDate(
                                                                segment.departureDate
                                                            )}
                                                        </Text>
                                                    </View>

                                                    <View className="flex-1 items-center relative">
                                                        <View className="relative h-0.5 bg-gray-200 w-full" />

                                                        <View className="rounded-full absolute -top-3 border-2 border-blue-500 bg-white p-1">
                                                            <Plane
                                                                size={12}
                                                                color="#3B82F6"
                                                                style={{
                                                                    transform: [
                                                                        {
                                                                            rotate: "90deg",
                                                                        },
                                                                    ],
                                                                }}
                                                            />
                                                        </View>

                                                        <View className="bg-white px-2 py-0.5 mt-6 rounded-xl border border-gray-200 absolute">
                                                            <Text className="text-xs text-gray-500">
                                                                {formatDuration(
                                                                    Number.parseInt(
                                                                        segment.duration.toString()
                                                                    )
                                                                )}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View className="items-center w-24">
                                                        <Text className="text-lg font-bold text-blue-600">
                                                            {formatTime(
                                                                segment.arrivalDate
                                                            )}
                                                        </Text>
                                                        <Text className="text-base font-medium my-1">
                                                            {capitalizeFirstLetter(
                                                                segment.to
                                                            )}
                                                        </Text>
                                                        <Text className="text-xs text-gray-500">
                                                            {formatDate(
                                                                segment.arrivalDate
                                                            )}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>
                                        )
                                    )}
                                </View>
                            )
                        )}
                    </View>
                </Card>

                {/* Passenger Details */}
                <Card className="mb-4 rounded-xl overflow-hidden">
                    <View className="bg-blue-600 p-4 flex-row items-center gap-3">
                        <View className="bg-blue-500/20 rounded-full w-10 h-10 items-center justify-center">
                            <User size={20} color="white" />
                        </View>
                        <Text className="text-white text-lg font-bold">
                            Passenger Information
                        </Text>
                    </View>

                    <View className="p-4">
                        {bookingForm.passengersDetails.map(
                            (passenger, index) => (
                                <View
                                    key={index}
                                    className="mb-4 border-b border-gray-100 pb-4"
                                >
                                    <View className="flex-row justify-between items-center mb-2">
                                        <Text className="text-base font-semibold">
                                            {passenger.firstName}{" "}
                                            {passenger.lastName}
                                        </Text>
                                        <View className="bg-gray-100 px-2 py-0.5 rounded-xl">
                                            <Text className="text-xs text-gray-600">
                                                {passenger.passengerType
                                                    .charAt(0)
                                                    .toUpperCase() +
                                                    passenger.passengerType.slice(
                                                        1
                                                    )}
                                            </Text>
                                        </View>
                                    </View>

                                    <View className="gap-2">
                                        <View className="flex-row items-center gap-2">
                                            <Calendar
                                                size={16}
                                                color="#6b7280"
                                            />
                                            <Text className="text-sm text-gray-600">
                                                DOB:{" "}
                                                {formatDate(
                                                    passenger.dateOfBirth
                                                )}
                                            </Text>
                                        </View>

                                        <View className="flex-row items-center gap-2">
                                            <MapPin size={16} color="#6b7280" />
                                            <Text className="text-sm text-gray-600">
                                                {passenger.passportInfo.country}
                                            </Text>
                                        </View>

                                        {passenger.seatAssignments &&
                                            passenger.seatAssignments.length >
                                                0 && (
                                                <View className="mt-2">
                                                    <Text className="text-sm font-medium mb-1">
                                                        Seat Assignments:
                                                    </Text>
                                                    {passenger.seatAssignments.map(
                                                        (seat, seatIndex) => {
                                                            const segment =
                                                                bookingForm.flightInfo.flights
                                                                    .flatMap(
                                                                        (f) =>
                                                                            f
                                                                                .flight
                                                                                .segments
                                                                    )
                                                                    .find(
                                                                        (s) =>
                                                                            s._id ===
                                                                            seat.segmentId
                                                                    );

                                                            return segment ? (
                                                                <View
                                                                    key={
                                                                        seatIndex
                                                                    }
                                                                    className="flex-row items-center gap-2 ml-2"
                                                                >
                                                                    <Check
                                                                        size={
                                                                            16
                                                                        }
                                                                        color="#22c55e"
                                                                    />
                                                                    <Text className="text-sm">
                                                                        {
                                                                            segment.from
                                                                        }{" "}
                                                                        →{" "}
                                                                        {
                                                                            segment.to
                                                                        }
                                                                        : Seat{" "}
                                                                        {
                                                                            seat.seatNumber
                                                                        }
                                                                    </Text>
                                                                </View>
                                                            ) : null;
                                                        }
                                                    )}
                                                </View>
                                            )}
                                    </View>
                                </View>
                            )
                        )}
                    </View>
                </Card>

                {/* Payment Summary */}
                <Card className="mb-4 rounded-xl overflow-hidden">
                    <View className="bg-blue-600 p-4 flex-row items-center gap-3">
                        <View className="bg-blue-500/20 rounded-full w-10 h-10 items-center justify-center">
                            <CreditCard size={20} color="white" />
                        </View>
                        <Text className="text-white text-lg font-bold">
                            Payment Summary
                        </Text>
                    </View>

                    <View className="p-4">
                        <View className="flex-row justify-between mb-2">
                            <Text className="text-sm text-gray-500">
                                Base Fare
                            </Text>
                            <Text className="text-sm">
                                $
                                {bookingForm.paymentInfo.subTotal?.toFixed(2) ||
                                    "0.00"}
                            </Text>
                        </View>

                        <View className="flex-row justify-between mb-2">
                            <Text className="text-sm text-gray-500">
                                Taxes & Fees
                            </Text>
                            <Text className="text-sm">
                                $
                                {bookingForm.paymentInfo.taxAmount?.toFixed(
                                    2
                                ) || "0.00"}
                            </Text>
                        </View>

                        {bookingForm.paymentInfo.charges > 0 && (
                            <View className="flex-row justify-between mb-2">
                                <Text className="text-sm text-gray-500">
                                    Additional Charges
                                </Text>
                                <Text className="text-sm">
                                    $
                                    {bookingForm.paymentInfo.charges?.toFixed(
                                        2
                                    ) || "0.00"}
                                </Text>
                            </View>
                        )}

                        <View className="h-px bg-gray-200 my-3" />

                        <View className="flex-row justify-between mt-1">
                            <Text className="text-base font-semibold">
                                Total Amount
                            </Text>
                            <Text className="text-lg font-bold text-blue-600">
                                ${calculateTotal().toFixed(2)}
                            </Text>
                        </View>
                    </View>
                </Card>
            </View>
        </ScrollView>
    );
}
