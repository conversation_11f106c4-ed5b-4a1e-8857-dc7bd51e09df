import PaymentDetails from "@/components/booking/PaymentDetails";
import {
    ArrowLeft,
    Calendar,
    Check,
    Clock,
    Plane,
    User,
} from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import { formatDate, formatTime } from "@/utils/dateFormat";
import { useQuery } from "@tanstack/react-query";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
    ActivityIndicator,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";

export default function ConfirmReservation() {
    const { id } = useLocalSearchParams();
    const router = useRouter();
    const { getBookingById } = useBookingStore();
    const [isPaymentValid, setIsPaymentValid] = useState(false);

    // Fetch booking details using React Query
    const {
        data: response,
        isLoading,
        isError,
        error,
        refetch,
    } = useQuery({
        queryKey: ["booking", id],
        queryFn: () => getBookingById(id as string),
        enabled: !!id,
    });

    const booking = response?.data;

    const handlePaymentValidationChange = (isValid: boolean) => {
        setIsPaymentValid(isValid);
    };

    const handlePaymentComplete = (success: boolean, data?: any) => {
        if (success) {
            Toast.show({
                type: "success",
                text1: "Payment completed successfully",
                text2: data?.booking?.PNR
                    ? `PNR: ${data.booking.PNR}`
                    : "Your booking has been confirmed",
                position: "top",
                visibilityTime: 1500,
            });
            router.push("/(tabs)/bookings");
        } else {
            Toast.show({
                type: "error",
                text1: "Payment failed",
                text2: data?.message || "Please try again",
                position: "top",
                visibilityTime: 1500,
            });
        }
    };

    if (isLoading) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-100 bg-white">
                    <TouchableOpacity
                        className="p-2 rounded-lg bg-gray-100"
                        onPress={() => router.back()}
                    >
                        <ArrowLeft size={20} color="#111827" />
                    </TouchableOpacity>
                    <Text className="text-lg font-semibold text-gray-900">
                        Complete Reservation
                    </Text>
                    <View className="w-10" />
                </View>

                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#4f46e5" />
                    <Text className="mt-3 text-base text-gray-500">
                        Loading reservation details...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (isError || !booking) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-100 bg-white">
                    <TouchableOpacity
                        className="p-2 rounded-lg bg-gray-100"
                        onPress={() => router.back()}
                    >
                        <ArrowLeft size={20} color="#111827" />
                    </TouchableOpacity>
                    <Text className="text-lg font-semibold text-gray-900">
                        Complete Reservation
                    </Text>
                    <View className="w-10" />
                </View>

                <View className="flex-1 justify-center items-center p-4">
                    <Text className="text-xl font-bold text-red-500 mb-2">
                        Error
                    </Text>
                    <Text className="text-base text-gray-500 text-center mb-6">
                        {error instanceof Error
                            ? error.message
                            : "Failed to fetch reservation details"}
                    </Text>
                    <TouchableOpacity
                        className="bg-indigo-600 px-6 py-3 rounded-lg"
                        onPress={() => refetch()}
                    >
                        <Text className="text-white font-medium">Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    // Check if the booking is already confirmed
    if (booking.status.toLowerCase() !== "reserved") {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-100 bg-white">
                    <TouchableOpacity
                        className="p-2 rounded-lg bg-gray-100"
                        onPress={() => router.back()}
                    >
                        <ArrowLeft size={20} color="#111827" />
                    </TouchableOpacity>
                    <Text className="text-lg font-semibold text-gray-900">
                        Booking Status
                    </Text>
                    <View className="w-10" />
                </View>

                <View className="flex-1 justify-center items-center p-4">
                    <View className="bg-green-100 p-6 rounded-xl items-center mb-6">
                        <Check size={48} color="#16a34a" />
                        <Text className="text-xl font-bold text-green-700 mt-4">
                            This booking is already confirmed
                        </Text>
                        <Text className="text-base text-gray-600 text-center mt-2">
                            No further payment is needed
                        </Text>
                    </View>

                    <TouchableOpacity
                        className="bg-indigo-600 px-6 py-3 rounded-lg"
                        onPress={() => router.push(`/bookings`)}
                    >
                        <Text className="text-white font-medium">
                            View Booking Details
                        </Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-gray-50" edges={["bottom"]}>
            <ScrollView className="flex-1">
                {/* Reservation Summary */}
                <View className="bg-white p-4 mb-4">
                    <Text className="text-lg font-bold mb-4">
                        Reservation Summary
                    </Text>

                    <View className="bg-blue-50 p-4 rounded-lg mb-4">
                        <View className="flex-row justify-between mb-2">
                            <Text className="text-gray-600">
                                Booking Number:
                            </Text>
                            <Text className="font-semibold">
                                {booking.bookingNumber}
                            </Text>
                        </View>
                        <View className="flex-row justify-between mb-2">
                            <Text className="text-gray-600">PNR:</Text>
                            <Text className="font-semibold">{booking.PNR}</Text>
                        </View>
                        <View className="flex-row justify-between mb-2">
                            <Text className="text-gray-600">Status:</Text>
                            <Text className="font-semibold text-blue-600">
                                Reserved
                            </Text>
                        </View>
                        {booking.reservationExpiry && (
                            <View className="flex-row justify-between">
                                <Text className="text-gray-600">Expires:</Text>
                                <Text className="font-semibold text-red-500">
                                    {formatDate(booking.reservationExpiry)}{" "}
                                    {formatTime(booking.reservationExpiry)}
                                </Text>
                            </View>
                        )}
                    </View>

                    {/* Flight Summary */}
                    <View className="mb-4">
                        <Text className="font-semibold mb-2">
                            Flight Details
                        </Text>
                        {booking.flightSegments.map(
                            (segment: any, index: number) => (
                                <View
                                    key={index}
                                    className="bg-gray-50 p-3 rounded-lg mb-2"
                                >
                                    <View className="flex-row items-center mb-2">
                                        <Plane
                                            size={16}
                                            color="#4f46e5"
                                            className="mr-2"
                                        />
                                        <Text className="font-medium">
                                            {segment.flight.flightNumber}
                                        </Text>
                                    </View>
                                    <View className="flex-row justify-between mb-1">
                                        <Text className="text-gray-600">
                                            From:
                                        </Text>
                                        <Text>{segment.from}</Text>
                                    </View>
                                    <View className="flex-row justify-between mb-1">
                                        <Text className="text-gray-600">
                                            To:
                                        </Text>
                                        <Text>{segment.to}</Text>
                                    </View>
                                    <View className="flex-row justify-between mb-1">
                                        <View className="flex-row items-center">
                                            <Calendar
                                                size={14}
                                                color="#6b7280"
                                                className="mr-1"
                                            />
                                            <Text className="text-sm">
                                                {formatDate(
                                                    segment.departureDate
                                                )}
                                            </Text>
                                        </View>
                                        <View className="flex-row items-center">
                                            <Clock
                                                size={14}
                                                color="#6b7280"
                                                className="mr-1"
                                            />
                                            <Text className="text-sm">
                                                {formatTime(
                                                    segment.departureTime || ""
                                                )}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            )
                        )}
                    </View>

                    {/* Passenger Summary */}
                    <View className="mb-4">
                        <Text className="font-semibold mb-2">Passengers</Text>
                        <View className="bg-gray-50 p-3 rounded-lg">
                            {booking.passengers.map(
                                (passenger: any, index: number) => (
                                    <View
                                        key={index}
                                        className="flex-row items-center mb-2"
                                    >
                                        <User
                                            size={16}
                                            color="#4f46e5"
                                            className="mr-2"
                                        />
                                        <Text>
                                            {passenger.firstName}{" "}
                                            {passenger.lastName}
                                        </Text>
                                        <Text className="ml-auto text-sm text-gray-500 capitalize">
                                            {passenger.passengerType}
                                        </Text>
                                    </View>
                                )
                            )}
                        </View>
                    </View>

                    {/* Payment Summary */}
                    <View>
                        <Text className="font-semibold mb-2">Payment</Text>
                        <View className="bg-gray-50 p-3 rounded-lg">
                            <View className="flex-row justify-between mb-1">
                                <Text className="text-gray-600">
                                    Total Amount:
                                </Text>
                                <Text className="font-semibold">
                                    $
                                    {booking.payment.totalAmount?.toFixed(2) ||
                                        "0.00"}
                                </Text>
                            </View>
                            <View className="flex-row justify-between">
                                <Text className="text-gray-600">
                                    Payment Status:
                                </Text>
                                <Text className="text-amber-600 font-medium">
                                    Pending
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Payment Options */}
                <View className="bg-white p-4 mb-4">
                    <Text className="text-lg font-bold mb-4">
                        Complete Payment
                    </Text>
                    <Text className="text-gray-600 mb-4">
                        Complete your payment to confirm this reservation.
                        Choose your preferred payment method below.
                    </Text>

                    <PaymentDetails
                        reservationId={id as string}
                        onPaymentValidation={handlePaymentValidationChange}
                        onPaymentComplete={handlePaymentComplete}
                        isReservationPayment={true}
                        initialAmount={booking.payment.totalAmount || 0}
                    />
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}
