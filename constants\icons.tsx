import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
export const Plane = (props: any) => (
    <MaterialIcons name="airplanemode-active" {...props} />
);

export const Download = (props: any) => (
    <MaterialIcons name="file-download" {...props} />
);

export const User = (props: any) => <MaterialIcons name="person" {...props} />;
export const Calendar = (props: any) => (
    <MaterialIcons name="calendar-today" {...props} />
);
export const Mail = (props: any) => <MaterialIcons name="email" {...props} />;
export const Phone = (props: any) => <MaterialIcons name="phone" {...props} />;
export const CreditCard = (props: any) => (
    <MaterialIcons name="credit-card" {...props} />
);
export const MapPin = (props: any) => (
    <MaterialIcons name="location-pin" {...props} />
);
export const AlertCircle = (props: any) => (
    <MaterialIcons name="error-outline" {...props} />
);
export const Check = (props: any) => <MaterialIcons name="check" {...props} />;
export const Loader2 = (props: any) => (
    <MaterialIcons name="refresh" {...props} />
);
export const Save = (props: any) => <MaterialIcons name="save" {...props} />;
export const Wallet = (props: any) => (
    <MaterialIcons name="account-balance-wallet" {...props} />
);
export const ChevronDown = (props: any) => (
    <MaterialIcons name="keyboard-arrow-down" {...props} />
);
export const ChevronUp = (props: any) => (
    <MaterialIcons name="keyboard-arrow-up" {...props} />
);
export const Minus = (props: any) => <MaterialIcons name="remove" {...props} />;
export const Globe = (props: any) => <MaterialIcons name="public" {...props} />;

// BookIcon, HomeIcon, SearchIcon, User2

export const BookIcon = (props: any) => (
    <MaterialIcons name="book" size={24} color="#000" {...props} />
);
export const HomeIcon = (props: any) => (
    <MaterialIcons name="home" size={24} color="#000" {...props} />
);
export const SearchIcon = (props: any) => (
    <MaterialIcons name="search" size={24} color="#000" {...props} />
);
export const User2 = (props: any) => (
    <MaterialIcons name="person" size={24} color="#000" {...props} />
);

// CheckCircle, Home, Ticket
export const CheckCircle = (props: any) => (
    <MaterialIcons name="check-circle" size={24} color="#000" {...props} />
);
export const Home = (props: any) => (
    <MaterialIcons name="home" size={24} color="#000" {...props} />
);
export const Ticket = (props: any) => (
    <MaterialIcons
        name="confirmation-number"
        size={24}
        color="#000"
        {...props}
    />
);

// ChevronRight, Eye, EyeOff
export const ChevronRight = (props: any) => (
    <MaterialIcons name="chevron-right" size={24} color="#000" {...props} />
);
export const Eye = (props: any) => (
    <MaterialIcons name="visibility" size={24} color="#000" {...props} />
);
export const EyeOff = (props: any) => (
    <MaterialIcons name="visibility-off" size={24} color="#000" {...props} />
);

// CheckCircle2, Clock, XCircle
export const CheckCircle2 = (props: any) => (
    <MaterialIcons name="check-circle" size={24} color="#000" {...props} />
);
export const Clock = (props: any) => (
    <MaterialIcons name="access-time" size={24} color="#000" {...props} />
);
export const XCircle = (props: any) => (
    <MaterialIcons name="cancel" size={24} color="#000" {...props} />
);

// ArrowDownRight, ArrowUpRight
export const ArrowDownRight = (props: any) => (
    <MaterialIcons name="arrow-downward" size={24} color="#000" {...props} />
);
export const ArrowUpRight = (props: any) => (
    <MaterialIcons name="arrow-upward" size={24} color="#000" {...props} />
);

// Calendar, Plane
export const Calendar2 = (props: any) => (
    <MaterialIcons name="calendar-today" size={24} color="#000" {...props} />
);
export const Plane2 = (props: any) => (
    <MaterialIcons
        name="airplanemode-active"
        size={24}
        color="#000"
        {...props}
    />
);

// ChevronLeft, MapPin, Plane, Shield
export const ChevronLeft = (props: any) => (
    <MaterialIcons name="chevron-left" size={24} color="#000" {...props} />
);
export const MapPin2 = (props: any) => (
    <MaterialIcons name="place" size={24} color="#000" {...props} />
);
export const Shield = (props: any) => (
    <MaterialIcons name="shield" size={24} color="#000" {...props} />
);
export const ArrowRight = (props: any) => (
    <MaterialIcons name="arrow-forward" size={24} color="#000" {...props} />
);
export const ArrowLeft = (props: any) => (
    <MaterialIcons name="arrow-back" size={24} color="#000" {...props} />
);
export const ArrowUp = (props: any) => (
    <MaterialIcons name="arrow-upward" size={24} color="#000" {...props} />
);
export const ArrowDown = (props: any) => (
    <MaterialIcons name="arrow-downward" size={24} color="#000" {...props} />
);
export const ArrowBack = (props: any) => (
    <MaterialIcons name="arrow-back" size={24} color="#000" {...props} />
);
export const ArrowForward = (props: any) => (
    <MaterialIcons name="arrow-forward" size={24} color="#000" {...props} />
);
export const ArrowRightCircle = (props: any) => (
    <MaterialIcons name="arrow-right-alt" size={24} color="#000" {...props} />
);
export const ArrowLeftCircle = (props: any) => (
    <MaterialIcons name="arrow-left-alt" size={24} color="#000" {...props} />
);
export const ArrowUpCircle = (props: any) => (
    <MaterialIcons name="arrow-upward" size={24} color="#000" {...props} />
);
export const ArrowDownCircle = (props: any) => (
    <MaterialIcons name="arrow-downward" size={24} color="#000" {...props} />
);

export const ArrowRightCircle2 = (props: any) => (
    <MaterialIcons name="arrow-right-alt" size={24} color="#000" {...props} />
);
export const DollarSign = (props: any) => (
    <MaterialIcons name="money" size={24} color="#000" {...props} />
);
export const Plus = (props: any) => (
    <MaterialIcons name="add" size={24} color="#000" {...props} />
);
export const MinusCircle = (props: any) => (
    <MaterialIcons name="remove-circle" size={24} color="#000" {...props} />
);

export const Trash = (props: any) => (
    <MaterialIcons name="delete" size={24} color="#000" {...props} />
);
export const Edit = (props: any) => (
    <MaterialIcons name="edit" size={24} color="#000" {...props} />
);
export const Filter = (props: any) => (
    <MaterialIcons name="filter-list" size={24} color="#000" {...props} />
);
export const Sort = (props: any) => (
    <MaterialIcons name="sort" size={24} color="#000" {...props} />
);
export const Share = (props: any) => (
    <MaterialIcons name="share" size={24} color="#000" {...props} />
);

export const Copy = (props: any) => (
    <MaterialIcons name="content-copy" size={24} color="#000" {...props} />
);

export const Star = (props: any) => (
    <MaterialIcons name="star" size={24} color="#000" {...props} />
);

export const FileText = (props: any) => (
    <MaterialIcons name="description" size={24} color="#000" {...props} />
);
export const ExternalLink = (props: any) => (
    <MaterialIcons name="open-in-new" size={24} color="#000" {...props} />
);

export const ArrowLeftRight = (props: any) => (
    <MaterialIcons name="swap-horiz" size={24} color="#000" {...props} />
);
export const Search = (props: any) => (
    <MaterialIcons name="search" size={24} color="#000" {...props} />
);
export const X = (props: any) => (
    <MaterialIcons name="close" size={24} color="#000" {...props} />
);

export const Lock = (props: any) => (
    <MaterialIcons name="lock" size={24} color="#000" {...props} />
);

export const Activity = (props: any) => (
    <MaterialIcons name="access-time" size={24} color="#000" {...props} />
);
export const Users = (props: any) => (
    <MaterialIcons name="people" size={24} color="#000" {...props} />
);

export const Briefcase = (props: any) => (
    <MaterialIcons name="work" size={24} color="#000" {...props} />
);
export const Building = (props: any) => (
    <MaterialIcons name="business" size={24} color="#000" {...props} />
);

export const HelpCircle = (props: any) => (
    <MaterialIcons name="help" size={24} color="#000" {...props} />
);
export const LogOut = (props: any) => (
    <MaterialIcons name="logout" size={24} color="#000" {...props} />
);
export const Settings = (props: any) => (
    <MaterialIcons name="settings" size={24} color="#000" {...props} />
);
export const Bell = (props: any) => (
    <MaterialIcons name="notifications" size={24} color="#000" {...props} />
);

export const Coffee = (props: any) => (
    <MaterialIcons name="local-cafe" size={24} color="#000" {...props} />
);
export const Luggage = (props: any) => (
    <MaterialIcons name="luggage" size={24} color="#000" {...props} />
);

export const Armchair = (props: any) => (
    <MaterialIcons name="chair" size={24} color="#000" {...props} />
);

export const Sunrise = (props: any) => (
    <MaterialIcons name="wb-sunny" size={24} color="#000" {...props} />
);

export const Sunset = (props: any) => (
    <MaterialIcons name="nights-stay" size={24} color="#000" {...props} />
);

const LucideIcon = (props: any) => (
    <MaterialIcons name="lucide-icon" size={24} color="#000" {...props} />
);

export const ShoppingCart = (props: any) => (
    <MaterialIcons name="shopping-cart" size={24} color="#000" {...props} />
);

export const Passport = (props: any) => (
    <MaterialIcons name="luggage" size={24} color="#000" {...props} />
);
