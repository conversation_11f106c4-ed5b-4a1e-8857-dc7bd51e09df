import React, { useState, useRef } from "react";
import { View, Text, FlatList, Animated, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { OnboardingItem } from "./OnboardingItem";
import { PaginationDot } from "./PaginationDot";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { images } from "@/constants/images";

const onboardingData = [
    {
        id: "1",
        title: "Welcome to SomExpress Airways",
        description:
            "Your Journey, Our Priority. Experience seamless travel with our award-winning service.",
        image: images.onboardingImg1,
    },
    {
        id: "2",
        title: "Book Flights with Ease",
        description:
            "Find and book flights with real-time pricing and availability. No hidden fees, just transparent booking.",
        image: images.onboardingImg2,
    },
    {
        id: "3",
        title: "Manage Your Bookings",
        description:
            "Check flight status, make changes to your booking, and get real-time updates about your journey.",
        image: images.onboardingImg3,
    },
    {
        id: "4",
        title: "Personalized Recommendations",
        description:
            "Discover new destinations based on your preferences and travel history. Your perfect trip is just a tap away.",
        image: images.onboardingImg4,
    },
];

export const OnboardingScreen: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const scrollX = useRef(new Animated.Value(0)).current;
    const slidesRef = useRef<FlatList>(null);
    const router = useRouter();

    const viewableItemsChanged = useRef(({ viewableItems }: any) => {
        if (viewableItems[0]) {
            setCurrentIndex(viewableItems[0].index);
        }
    }).current;

    const viewConfig = useRef({ viewAreaCoveragePercentThreshold: 50 }).current;

    const scrollTo = (index: number) => {
        if (slidesRef.current) {
            slidesRef.current.scrollToIndex({ index });
        }
    };

    const handleNext = () => {
        if (currentIndex < onboardingData.length - 1) {
            scrollTo(currentIndex + 1);
        } else {
            router.replace("/login");
        }
    };

    const handleSkip = () => {
        router.replace("/login");
    };

    return (
        <View className="flex-1 bg-white">
            <View className="z-10 absolute top-10 right-0 flex-row justify-between items-center px-5 py-3">
                <TouchableOpacity className="p-2.5" onPress={handleSkip}>
                    <Text className="text-base text-blue-500 font-medium">
                        Skip
                    </Text>
                </TouchableOpacity>
            </View>
            <View className="flex-1">
                <FlatList
                    data={onboardingData}
                    renderItem={({ item }) => <OnboardingItem item={item} />}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    pagingEnabled
                    bounces={false}
                    keyExtractor={(item) => item.id}
                    onScroll={Animated.event(
                        [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                        { useNativeDriver: false }
                    )}
                    onViewableItemsChanged={viewableItemsChanged}
                    viewabilityConfig={viewConfig}
                    ref={slidesRef}
                    scrollEventThrottle={32}
                />
            </View>

            <View className="py-8 px-5">
                <View className="flex-row justify-center mb-5">
                    {onboardingData.map((_, index) => (
                        <PaginationDot
                            key={index}
                            index={index}
                            currentIndex={currentIndex}
                            length={onboardingData.length}
                        />
                    ))}
                </View>

                <View className="flex-row items-center px-5">
                    <TouchableOpacity
                        className="w-full h-16 rounded-full overflow-hidden shadow-md bg-blue-500 flex-row items-center justify-center"
                        onPress={handleNext}
                    >
                        <Text className="text-white text-lg font-semibold mr-2">
                            {currentIndex === onboardingData.length - 1
                                ? "Get Started"
                                : "Next"}
                        </Text>
                        <MaterialCommunityIcons
                            name={
                                currentIndex === onboardingData.length - 1
                                    ? "rocket-launch"
                                    : "arrow-right"
                            }
                            size={24}
                            color="white"
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};
