import React from "react";
import { View, Text } from "react-native";
import { CheckCircle2, Clock, XCircle } from "@/constants/icons";

type StatusType = "APPROVED" | "PENDING" | "REJECTED";

interface StatusBadgeProps {
    status: StatusType;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
    const getStatusColor = () => {
        switch (status) {
            case "APPROVED":
                return "#10B981"; // emerald-500 (success)
            case "PENDING":
                return "#F59E0B"; // amber-500 (warning)
            case "REJECTED":
                return "#EF4444"; // red-500 (error)
            default:
                return "#9CA3AF"; // gray-400 (inactive)
        }
    };

    const getStatusText = () => {
        switch (status) {
            case "APPROVED":
                return "Approved";
            case "PENDING":
                return "Pending";
            case "REJECTED":
                return "Rejected";
            default:
                return "Unknown";
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case "APPROVED":
                return <CheckCircle2 size={14} color="#10B981" />; // emerald-500
            case "PENDING":
                return <Clock size={14} color="#F59E0B" />; // amber-500
            case "REJECTED":
                return <XCircle size={14} color="#EF4444" />; // red-500
            default:
                return null;
        }
    };

    const statusColor = getStatusColor();
    const statusText = getStatusText();
    const statusIcon = getStatusIcon();

    // Get the background color with opacity
    const getBackgroundColor = () => {
        switch (status) {
            case "APPROVED":
                return "bg-emerald-500/10";
            case "PENDING":
                return "bg-amber-500/10";
            case "REJECTED":
                return "bg-red-500/10";
            default:
                return "bg-gray-400/10";
        }
    };

    // Get the text color
    const getTextColor = () => {
        switch (status) {
            case "APPROVED":
                return "text-emerald-500";
            case "PENDING":
                return "text-amber-500";
            case "REJECTED":
                return "text-red-500";
            default:
                return "text-gray-400";
        }
    };

    return (
        <View
            className={`flex-row items-center py-1 px-2 rounded-full self-start ${getBackgroundColor()}`}
        >
            {statusIcon}
            <Text className={`text-xs font-medium ml-1 ${getTextColor()}`}>
                {statusText}
            </Text>
        </View>
    );
};
