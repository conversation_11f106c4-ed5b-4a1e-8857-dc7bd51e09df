import { useBookingStore } from "@/stores/booking-store";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { Text, View } from "react-native";

export function PaymentStatusAlerts() {
    const { bookingForm } = useBookingStore();

    // Get mutation states from the query cache
    const walletPaymentMutation = useMutation({
        mutationKey: ["walletPayment"],
    });

    const cashPaymentMutation = useMutation({
        mutationKey: ["cashPayment"],
    });

    const reservationMutation = useMutation({
        mutationKey: ["reservation"],
    });

    // Format date for reservation expiry
    const formatExpiryDate = (dateString: string) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        return date.toLocaleString("en-US", {
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    return (
        <View className="mt-4">
            {/* Success/Error Messages */}
            {(walletPaymentMutation.isError || cashPaymentMutation.isError) && (
                <View className="bg-red-100 border border-red-200 rounded-lg p-4 mb-3">
                    <Text className="font-bold text-red-700">
                        Payment Failed
                    </Text>
                    <Text className="mt-1 text-gray-700">
                        There was an error processing your payment. Please try
                        again or contact customer support.
                    </Text>
                </View>
            )}

            {(walletPaymentMutation.isSuccess ||
                cashPaymentMutation.isSuccess) && (
                <View className="bg-green-100 border border-green-200 rounded-lg p-4 mb-3">
                    <Text className="font-bold text-green-700">
                        Payment Successful
                    </Text>
                    <Text className="mt-1 text-gray-700">
                        Your payment has been processed and booking has been
                        confirmed.
                    </Text>
                    <Text className="mt-2 font-medium">
                        Booking reference:{" "}
                        {bookingForm.bookingReference?.bookingNumber}
                    </Text>
                </View>
            )}

            {reservationMutation.isSuccess && (
                <View className="bg-blue-100 border border-blue-200 rounded-lg p-4 mb-3">
                    <Text className="font-bold text-blue-800">
                        Reservation Confirmed
                    </Text>
                    <Text className="mt-1 text-gray-700">
                        Your booking has been reserved until{" "}
                        {formatExpiryDate(
                            bookingForm.bookingReference?.expiresAt || ""
                        )}
                    </Text>
                    <Text className="mt-2 font-medium">
                        Reference: {bookingForm.bookingReference?.bookingNumber}
                    </Text>
                </View>
            )}

            {reservationMutation.isError && (
                <View className="bg-red-100 border border-red-200 rounded-lg p-4 mb-3">
                    <Text className="font-bold text-red-700">
                        Reservation Failed
                    </Text>
                    <Text className="mt-1 text-gray-700">
                        There was an error reserving your booking. Please try
                        again.
                    </Text>
                </View>
            )}
        </View>
    );
}
