import React, { useEffect, useRef, useState } from "react";
import {
    ActivityIndicator,
    Animated,
    Easing,
    LayoutAnimation,
    Platform,
    Text,
    TouchableOpacity,
    UIManager,
    View,
} from "react-native";
import {
    ArrowRight,
    Calendar,
    Check,
    ChevronDown,
    Clock,
    Coffee,
    Luggage,
    Plane,
    Star,
} from "../../constants/icons";
import {
    ClassOption,
    DateOption,
    Flight,
    Passengers,
} from "../../types/flight";
import { Card } from "../ui/card";

interface FlightOptionProps {
    flight: Flight;
    onSelect: (option: ClassOption) => void;
    selectedClass?: string;
    passengers: Passengers;
    dateOptions: DateOption[];
    selectedDate: string;
    onDateSelect: (date: string) => void;
    onSearch?: (legIndex: number) => void;
    isSearching?: boolean;
}

export function FlightOption({
    flight,
    onSelect,
    selectedClass,
    passengers,
    dateOptions,
    selectedDate,
    onDateSelect,
    onSearch,
    isSearching = false,
}: FlightOptionProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const expandAnimation = useRef(new Animated.Value(0)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.98)).current;
    const planePosition = useRef(new Animated.Value(0)).current;
    const segment = flight.segments[0];
    const scrollViewRef = useRef(null);
    const [localSelectedDate, setLocalSelectedDate] = useState(selectedDate);

    // Animation on component mount
    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 400,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 300,
                easing: Easing.out(Easing.cubic),
                useNativeDriver: true,
            }),
        ]).start();

        // Animate plane moving across the route
        Animated.loop(
            Animated.timing(planePosition, {
                toValue: 1,
                duration: 3000,
                easing: Easing.linear,
                useNativeDriver: true,
            })
        ).start();
    }, []);

    // Update local state when prop changes
    useEffect(() => {
        setLocalSelectedDate(selectedDate);
    }, [selectedDate]);

    const toggleExpand = () => {
        // Configure the next layout animation
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

        const toValue = isExpanded ? 0 : 1;

        Animated.timing(expandAnimation, {
            toValue,
            duration: 300,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
            useNativeDriver: false,
        }).start();

        setIsExpanded(!isExpanded);
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return "";
        return new Date(dateString).toLocaleDateString(undefined, {
            weekday: "short",
            day: "numeric",
            month: "short",
        });
    };

    const formatDuration = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}h ${remainingMinutes}m`;
    };

    const calculateTotalPrice = (pricing: any) => {
        if (!pricing) return 0;
        return (
            pricing.adult * passengers.adult +
            pricing.child * passengers.child +
            pricing.infant * passengers.infant
        );
    };

    const capitalizeFirstLetter = (string: string) => {
        return string.charAt(0).toUpperCase() + string.slice(1);
    };

    // Calculate total number of passengers
    const totalPassengers = passengers.adult + passengers.child;

    // Handle date selection with automatic search
    const handleDateSelect = (date: string) => {
        setLocalSelectedDate(date);
        onDateSelect(date);

        // Automatically trigger search when date is selected
        if (onSearch) {
            onSearch(0);
        }
    };

    // Auto-scroll to selected date
    useEffect(() => {
        if (scrollViewRef.current && dateOptions.length > 0) {
            const selectedIndex = dateOptions.findIndex(
                (item) => item.date === localSelectedDate
            );
            if (selectedIndex !== -1) {
                // This would work if using ScrollView with scrollTo
                // scrollViewRef.current.scrollTo({ x: selectedIndex * 85, animated: true });
            }
        }
    }, [localSelectedDate, dateOptions]);

    // Render date options horizontally
    const renderDateOptions = () => {
        if (dateOptions.length === 0) return null;

        return (
            <View className="mb-3 rounded-lg bg-gray-50 p-3">
                <View className="mb-2 flex-row items-center justify-between">
                    <View className="flex-row items-center">
                        <Calendar size={16} color="#3B82F6" />
                        <Text className="ml-2 text-sm font-medium text-gray-700">
                            Available Dates
                        </Text>
                    </View>
                    <Text className="text-xs font-medium text-blue-600">
                        Swipe for more
                    </Text>
                </View>
                <Animated.ScrollView
                    ref={scrollViewRef}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ paddingVertical: 4 }}
                    snapToInterval={85} // Width + margin
                    decelerationRate="fast"
                >
                    {dateOptions.map((dateOption, index) => {
                        const date = new Date(dateOption.date);
                        const isSelected =
                            dateOption.date === localSelectedDate;
                        const isBestDeal = dateOption.isBestDeal;
                        const month = new Intl.DateTimeFormat("en-US", {
                            month: "short",
                        }).format(date);
                        const day = date.getDate();
                        const weekday = new Intl.DateTimeFormat("en-US", {
                            weekday: "short",
                        }).format(date);

                        return (
                            <Animated.View
                                key={dateOption.date}
                                style={{
                                    opacity: fadeAnim,
                                    transform: [
                                        { scale: isSelected ? 1.02 : 1 },
                                        { translateY: isSelected ? -2 : 0 },
                                    ],
                                }}
                            >
                                <TouchableOpacity
                                    className={`mr-2 overflow-hidden rounded-lg border ${
                                        isSelected
                                            ? "border-2 border-blue-500"
                                            : isBestDeal && !isSelected
                                            ? "border-blue-400"
                                            : "border-gray-200"
                                    } ${
                                        !dateOption.available
                                            ? "opacity-70"
                                            : ""
                                    }`}
                                    onPress={() =>
                                        dateOption.available &&
                                        handleDateSelect(dateOption.date)
                                    }
                                    disabled={
                                        !dateOption.available || isSearching
                                    }
                                    activeOpacity={0.7}
                                    style={{
                                        width: 80,
                                        shadowColor: isSelected
                                            ? "#3B82F6"
                                            : "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: isSelected ? 3 : 1,
                                        },
                                        shadowOpacity: isSelected ? 0.3 : 0.1,
                                        shadowRadius: isSelected ? 4 : 2,
                                        elevation: isSelected ? 4 : 2,
                                    }}
                                >
                                    {isBestDeal && (
                                        <View className="absolute -right-10 top-1 z-10 rotate-45 bg-blue-500 px-6 py-0.5">
                                            <Text className="text-[8px] font-bold text-white">
                                                BEST DEAL
                                            </Text>
                                        </View>
                                    )}
                                    <View
                                        className={`w-full py-1 ${
                                            isSelected
                                                ? "bg-blue-100"
                                                : isBestDeal
                                                ? "bg-blue-50"
                                                : "bg-gray-100"
                                        }`}
                                    >
                                        <Text
                                            className={`text-center text-xs font-medium ${
                                                isSelected
                                                    ? "text-blue-700"
                                                    : "text-gray-700"
                                            }`}
                                        >
                                            {weekday}
                                        </Text>
                                    </View>
                                    <View className="items-center bg-white px-1 py-2">
                                        <Text
                                            className={`text-xs font-bold ${
                                                isSelected
                                                    ? "text-blue-800"
                                                    : "text-gray-800"
                                            }`}
                                        >
                                            {month} {day}
                                        </Text>
                                        {dateOption.price !== undefined &&
                                            dateOption.price !== null && (
                                                <Text
                                                    className={`mt-1 text-xs font-bold ${
                                                        isBestDeal
                                                            ? "text-blue-600"
                                                            : "text-gray-700"
                                                    }`}
                                                >
                                                    $
                                                    {typeof dateOption.price ===
                                                    "string"
                                                        ? parseFloat(
                                                              dateOption.price
                                                          )
                                                        : dateOption.price}
                                                </Text>
                                            )}
                                        {dateOption.available ? (
                                            <View className="mt-1 flex-row items-center rounded-full bg-green-100 px-1.5 py-0.5">
                                                <View className="mr-1 h-1.5 w-1.5 rounded-full bg-green-600"></View>
                                                <Text className="text-[8px] font-medium text-green-700">
                                                    Available
                                                </Text>
                                            </View>
                                        ) : (
                                            <View className="mt-1 flex-row items-center rounded-full bg-gray-100 px-1.5 py-0.5">
                                                <View className="mr-1 h-1.5 w-1.5 rounded-full bg-gray-400"></View>
                                                <Text className="text-[8px] font-medium text-gray-500">
                                                    Unavailable
                                                </Text>
                                            </View>
                                        )}
                                    </View>
                                </TouchableOpacity>
                            </Animated.View>
                        );
                    })}
                </Animated.ScrollView>
            </View>
        );
    };

    const rotateChevron = expandAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: ["0deg", "180deg"],
    });

    return (
        <Animated.View
            className="mb-4"
            style={{
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
            }}
        >
            <Card className="overflow-hidden rounded-xl bg-white shadow-sm">
                {isSearching && (
                    <View className="absolute inset-0 z-50 flex items-center justify-center rounded-xl bg-black/20">
                        <View className="flex-row items-center rounded-lg bg-white p-4">
                            <ActivityIndicator size="small" color="#3B82F6" />
                            <Text className="ml-2 text-sm font-medium text-gray-700">
                                Searching flights...
                            </Text>
                        </View>
                    </View>
                )}

                <View className="border-b border-blue-700 bg-blue-500 p-3">
                    <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <View className="mr-3 h-10 w-10 items-center justify-center rounded-full border-2 border-blue-300 bg-white">
                                <Plane size={20} color="#3B82F6" />
                            </View>
                            <View>
                                <Text className="text-sm font-bold text-white">
                                    {flight.aircraft.model}
                                </Text>
                                <View className="flex-row items-center">
                                    <Text className="text-xs text-blue-100">
                                        {flight.flightNumber}
                                    </Text>
                                    <Text className="mx-1 text-xs text-blue-200">
                                        |
                                    </Text>
                                    <Text className="text-xs text-blue-100">
                                        {formatDuration(
                                            segment.schedule.duration
                                        )}
                                    </Text>
                                </View>
                            </View>
                        </View>

                        <View className="flex-row">
                            <View className="mr-2 rounded-full bg-white px-2 py-1">
                                <Text className="text-xs font-bold text-blue-600">
                                    {capitalizeFirstLetter(
                                        segment.route.origin.city
                                    )}
                                </Text>
                            </View>
                            <View className="rounded-full border border-green-200 bg-green-50 px-2 py-1">
                                <Text className="text-xs font-bold text-green-600">
                                    {segment.status || "On Time"}
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                <View className="p-3">
                    <View className="mb-3 rounded-lg bg-gray-50 p-2">
                        <Text className="text-xs text-gray-600">
                            Included in your fare:
                        </Text>
                        <View className="flex-row">
                            <View className="mr-3 flex-row items-center">
                                <Luggage
                                    size={12}
                                    color="#10B981"
                                    className="mr-1"
                                />
                                <Text className="text-xs text-gray-700">
                                    20 Kg
                                </Text>
                            </View>
                            <View className="flex-row items-center">
                                <Coffee
                                    size={12}
                                    color="#10B981"
                                    className="mr-1"
                                />
                                <Text className="text-xs text-gray-700">
                                    Meals
                                </Text>
                            </View>
                        </View>
                    </View>

                    <View className="mb-3 rounded-lg border border-gray-100 bg-white p-3">
                        <View className="mb-4 flex-row justify-between">
                            <View className="items-start">
                                <Text className="text-xl font-bold text-gray-800">
                                    {segment.schedule.departure.time}
                                </Text>
                                <Text className="mt-1 text-xs text-gray-500">
                                    {formatDate(
                                        segment.schedule.departure.date
                                    )}
                                </Text>
                            </View>

                            <View className="mx-2 flex-1 items-center justify-center">
                                <View className="relative h-0.5 w-full bg-blue-200">
                                    <Animated.View
                                        style={{
                                            position: "absolute",
                                            top: -8,
                                            left: 0,
                                            transform: [
                                                {
                                                    translateX:
                                                        planePosition.interpolate(
                                                            {
                                                                inputRange: [
                                                                    0, 1,
                                                                ],
                                                                outputRange: [
                                                                    "0%",
                                                                    "100%",
                                                                ],
                                                            }
                                                        ),
                                                },
                                            ],
                                        }}
                                    >
                                        <View className="rounded-full border-2 border-blue-500 bg-white p-1">
                                            <Plane
                                                size={12}
                                                color="#3B82F6"
                                                style={{
                                                    transform: [
                                                        { rotate: "90deg" },
                                                    ],
                                                }}
                                            />
                                        </View>
                                    </Animated.View>
                                </View>
                            </View>

                            <View className="items-end">
                                <Text className="text-xl font-bold text-gray-800">
                                    {segment.schedule.arrival.time}
                                </Text>
                                <Text className="mt-1 text-xs text-gray-500">
                                    {formatDate(segment.schedule.arrival.date)}
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row items-center justify-between">
                            <View className="flex-1">
                                <Text className="text-sm font-bold text-gray-800">
                                    {capitalizeFirstLetter(
                                        segment.route.origin.city
                                    )}
                                </Text>
                                <Text className="text-xs text-gray-500">
                                    {segment.route.origin.airport?.toUpperCase()}
                                </Text>
                            </View>

                            <View className="mx-2 flex-row items-center rounded-full bg-gray-100 px-2 py-1">
                                <Clock size={12} color="#4B5563" />
                                <Text className="ml-1 text-xs font-medium text-gray-600">
                                    {formatDuration(segment.schedule.duration)}
                                </Text>
                            </View>

                            <View className="flex-1 items-end">
                                <Text className="text-sm font-bold text-gray-800">
                                    {capitalizeFirstLetter(
                                        segment.route.destination.city
                                    )}
                                </Text>
                                <Text className="text-xs text-gray-500">
                                    {segment.route.destination.airport?.toUpperCase()}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Date options */}
                    {dateOptions.length > 0 && renderDateOptions()}

                    <TouchableOpacity
                        className="flex-row items-center justify-center border-t border-gray-200 py-2"
                        onPress={toggleExpand}
                        activeOpacity={0.7}
                        disabled={isSearching}
                    >
                        <Animated.View
                            style={{ transform: [{ rotate: rotateChevron }] }}
                        >
                            <ChevronDown size={18} color="#3B82F6" />
                        </Animated.View>
                        <Text className="ml-2 text-sm font-medium text-blue-500">
                            {isExpanded ? "Show less" : "View cabin options"}
                        </Text>
                    </TouchableOpacity>

                    {isExpanded && (
                        <View className="border-t border-gray-200 pt-3">
                            <Text className="mb-3 text-base font-bold text-gray-800">
                                Select Your Cabin Class
                            </Text>

                            {segment.cabinOptions?.map((option, index) => {
                                const availableSeats = option.availableSeats;
                                const isFull =
                                    option.isFull || availableSeats === 0;
                                const hasEnoughSeats =
                                    option.hasEnoughSeats === true ||
                                    availableSeats >= totalPassengers;
                                const notEnoughSeats =
                                    !isFull &&
                                    availableSeats > 0 &&
                                    availableSeats < totalPassengers;
                                const isNotConfigured =
                                    !option.hasEnoughSeats &&
                                    !option.isFull &&
                                    option.pricing &&
                                    Object.values(option.pricing).every(
                                        (p) => p === 0
                                    );
                                const hasPricing =
                                    option.pricing &&
                                    calculateTotalPrice(option.pricing) > 0;
                                const isSelected =
                                    selectedClass === option.class;

                                return (
                                    <Animated.View
                                        key={option.class}
                                        style={{
                                            opacity: fadeAnim,
                                            transform: [
                                                {
                                                    translateY:
                                                        fadeAnim.interpolate({
                                                            inputRange: [0, 1],
                                                            outputRange: [
                                                                20 *
                                                                    (index + 1),
                                                                0,
                                                            ],
                                                        }),
                                                },
                                            ],
                                        }}
                                    >
                                        <View
                                            className={`flex-row items-center justify-between border-b border-gray-100 py-3 ${
                                                index ===
                                                segment.cabinOptions.length - 1
                                                    ? "border-b-0"
                                                    : ""
                                            } ${
                                                isSelected
                                                    ? "-mx-2 rounded-lg bg-blue-50 px-2"
                                                    : ""
                                            }`}
                                        >
                                            <View className="flex-1">
                                                <View className="flex-row items-center">
                                                    <Text
                                                        className={`mr-2 text-sm font-bold ${
                                                            isSelected
                                                                ? "text-blue-700"
                                                                : "text-gray-800"
                                                        }`}
                                                    >
                                                        {option.class}
                                                    </Text>
                                                    {option.isFull && (
                                                        <View className="rounded-full border border-red-200 bg-red-100 px-1.5 py-0.5">
                                                            <Text className="text-xs font-bold text-red-600">
                                                                Full
                                                            </Text>
                                                        </View>
                                                    )}
                                                    {!option.isFull &&
                                                        option.class ===
                                                            "Business" && (
                                                            <View className="flex-row items-center">
                                                                <Star
                                                                    size={10}
                                                                    color="#F59E0B"
                                                                />
                                                                <Star
                                                                    size={10}
                                                                    color="#F59E0B"
                                                                />
                                                                <Star
                                                                    size={10}
                                                                    color="#F59E0B"
                                                                />
                                                            </View>
                                                        )}
                                                </View>

                                                <View className="mt-1">
                                                    <View className="flex-row items-center">
                                                        <View className="mr-1 h-4 w-4 items-center justify-center rounded-full bg-green-100">
                                                            <Check
                                                                size={10}
                                                                color="#10B981"
                                                            />
                                                        </View>
                                                        <Text className="text-xs text-gray-600">
                                                            Baggage
                                                        </Text>
                                                    </View>
                                                    <View className="mt-1 flex-row items-center">
                                                        <View className="mr-1 h-4 w-4 items-center justify-center rounded-full bg-green-100">
                                                            <Check
                                                                size={10}
                                                                color="#10B981"
                                                            />
                                                        </View>
                                                        <Text className="text-xs text-gray-600">
                                                            Meals
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>

                                            <View className="flex-1 items-center">
                                                <Text
                                                    className={`text-base font-bold ${
                                                        isSelected
                                                            ? "text-blue-700"
                                                            : "text-blue-600"
                                                    }`}
                                                >
                                                    $
                                                    {calculateTotalPrice(
                                                        option.pricing
                                                    ).toLocaleString()}
                                                </Text>
                                                {option.class === "Economy" && (
                                                    <View className="mt-1 rounded-full bg-green-100 px-2 py-0.5">
                                                        <Text className="text-xs font-bold text-green-700">
                                                            Best Value
                                                        </Text>
                                                    </View>
                                                )}
                                            </View>

                                            <View className="flex-1 items-end">
                                                {isNotConfigured ? (
                                                    <TouchableOpacity
                                                        className="rounded-lg bg-gray-200 px-3 py-1.5"
                                                        disabled={true}
                                                    >
                                                        <Text className="text-xs text-gray-500">
                                                            Not Available
                                                        </Text>
                                                    </TouchableOpacity>
                                                ) : isFull ? (
                                                    <TouchableOpacity
                                                        className="rounded-lg bg-gray-200 px-3 py-1.5"
                                                        disabled={true}
                                                    >
                                                        <Text className="text-xs text-gray-500">
                                                            Fully Booked
                                                        </Text>
                                                    </TouchableOpacity>
                                                ) : notEnoughSeats ? (
                                                    <TouchableOpacity
                                                        className="rounded-lg bg-gray-200 px-3 py-1.5"
                                                        disabled={true}
                                                    >
                                                        <Text className="text-xs text-gray-500">
                                                            Only{" "}
                                                            {availableSeats}{" "}
                                                            left
                                                        </Text>
                                                    </TouchableOpacity>
                                                ) : !hasPricing ? (
                                                    <Text className="text-xs text-gray-500">
                                                        Not Available
                                                    </Text>
                                                ) : (
                                                    <View>
                                                        <TouchableOpacity
                                                            className={`rounded-lg px-3 py-1.5 ${
                                                                isSelected
                                                                    ? "bg-blue-600"
                                                                    : "border-2 border-blue-500 bg-white"
                                                            }`}
                                                            onPress={() =>
                                                                onSelect(option)
                                                            }
                                                            disabled={
                                                                isSearching
                                                            }
                                                            style={{
                                                                shadowColor:
                                                                    isSelected
                                                                        ? "#3B82F6"
                                                                        : "transparent",
                                                                shadowOffset: {
                                                                    width: 0,
                                                                    height: 2,
                                                                },
                                                                shadowOpacity:
                                                                    isSelected
                                                                        ? 0.3
                                                                        : 0,
                                                                shadowRadius: 3,
                                                                elevation:
                                                                    isSelected
                                                                        ? 3
                                                                        : 0,
                                                            }}
                                                        >
                                                            {isSelected ? (
                                                                <View className="flex-row items-center">
                                                                    <Check
                                                                        size={
                                                                            14
                                                                        }
                                                                        color="#fff"
                                                                    />
                                                                    <Text className="ml-1 text-xs text-white">
                                                                        Selected
                                                                    </Text>
                                                                </View>
                                                            ) : (
                                                                <View className="flex-row items-center">
                                                                    <Text className="mr-1 text-xs text-blue-600">
                                                                        Select
                                                                    </Text>
                                                                    <ArrowRight
                                                                        size={
                                                                            14
                                                                        }
                                                                        color="#3B82F6"
                                                                    />
                                                                </View>
                                                            )}
                                                        </TouchableOpacity>

                                                        {hasEnoughSeats &&
                                                            availableSeats <=
                                                                5 && (
                                                                <Text className="mt-1 text-right text-xs text-amber-500">
                                                                    Only{" "}
                                                                    {
                                                                        availableSeats
                                                                    }{" "}
                                                                    seat
                                                                    {availableSeats !==
                                                                    1
                                                                        ? "s"
                                                                        : ""}{" "}
                                                                    left
                                                                </Text>
                                                            )}
                                                    </View>
                                                )}
                                            </View>
                                        </View>
                                    </Animated.View>
                                );
                            })}
                        </View>
                    )}
                </View>
            </Card>
        </Animated.View>
    );
}
