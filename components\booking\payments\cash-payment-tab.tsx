import { CreditCard } from "@/constants/icons"
import React from "react"
import { StyleSheet, Text, View } from "react-native"

export function CashPaymentTab() {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <CreditCard size={24} color="#d97706" />
        </View>
        <View>
          <Text style={styles.title}>Cash Payment</Text>
          <Text style={styles.description}>Pay at our counter using cash</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 20,
    backgroundColor: "#fffbeb",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  iconContainer: {
    backgroundColor: "#fef3c7",
    padding: 12,
    borderRadius: 20,
  },
  title: {
    fontSize: 16,
    fontWeight: "500",
  },
  description: {
    fontSize: 14,
    color: "#6b7280",
    marginTop: 4,
  },
})
