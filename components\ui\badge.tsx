import { Colors } from "@/constants/colors";
import React from "react";
import { StyleSheet, Text, View, ViewProps } from "react-native";

interface BadgeProps extends ViewProps {
  variant?: "default" | "outline" | "secondary" | "destructive" | "success";
  size?: "sm" | "md" | "lg";
  text?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = "default",
  size = "md",
  text,
  style,
  ...props
}) => {
  return (
    <View
      style={[
        styles.badge,
        styles[`badge_${variant}`],
        styles[`badge_${size}`],
        style,
      ]}
      {...props}
    >
      {text ? (
        <Text style={[styles.text, styles[`text_${variant}`]]}>{text}</Text>
      ) : (
        children
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  badge_default: {
    backgroundColor: Colors.primary,
  },
  badge_outline: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  badge_secondary: {
    backgroundColor: Colors.secondary,
  },
  badge_destructive: {
    backgroundColor: Colors.error,
  },
  badge_success: {
    backgroundColor: Colors.success,
  },
  badge_sm: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badge_md: {
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  badge_lg: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  text: {
    fontSize: 12,
    fontWeight: "500",
  },
  text_default: {
    color: "#fff",
  },
  text_outline: {
    color: Colors.primary,
  },
  text_secondary: {
    color: "#fff",
  },
  text_destructive: {
    color: "#fff",
  },
  text_success: {
    color: "#fff",
  },
});