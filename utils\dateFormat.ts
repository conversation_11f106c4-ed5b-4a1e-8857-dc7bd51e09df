export const formatDuration = (durationInMinutes: number) => {
    const hours = Math.floor(durationInMinutes / 60);
    const minutes = durationInMinutes % 60;
    return `${hours}h ${minutes}m`;
};

export const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
        timeZone: "UTC", // Adjust this based on your needs
    });
};

export const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString("en-US", {
        weekday: "short",
        month: "short",
        day: "numeric",
        timeZone: "UTC", // Adjust this based on your needs
    });
};

export function FindDuration(departureTime: string, arrivalTime: string) {
    const departure = new Date(`2021-01-01T${departureTime}`);
    const arrival = new Date(`2021-01-01T${arrivalTime}`);
    const duration = Math.abs(arrival.getTime() - departure.getTime()) / 60000;
    return formatDuration(duration);
}

export function combineDateTime(dateStr: string, timeStr: string): string {
    const date = new Date(dateStr);
    const [hours, minutes] = timeStr.split(":").map(Number);
    date.setUTCHours(hours, minutes, 0, 0);
    return date.toLocaleString("en-US", {
        weekday: "short",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "UTC",
    });
}

export function capitalizeFirstLetter(string: string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}
