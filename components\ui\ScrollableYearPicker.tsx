import React from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { X } from "../../constants/icons";

interface ScrollableYearPickerProps {
    years: number[];
    selectedYear: number;
    onYearSelect: (year: number) => void;
    onClose: () => void;
}

export default function ScrollableYearPicker({
    years,
    selectedYear,
    onYearSelect,
    onClose,
}: ScrollableYearPickerProps) {
    return (
        <View style={{ width: "100%" }}>
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: 16,
                    paddingBottom: 8,
                    borderBottomWidth: 1,
                    borderBottomColor: "#f1f5f9",
                }}
            >
                <Text
                    style={{
                        fontSize: 18,
                        fontWeight: "600",
                        color: "#0f172a",
                    }}
                >
                    Select Year
                </Text>
                <TouchableOpacity
                    style={{
                        padding: 8,
                        borderRadius: 20,
                        backgroundColor: "#f1f5f9",
                    }}
                    onPress={onClose}
                >
                    <X size={18} color="#64748b" />
                </TouchableOpacity>
            </View>

            <ScrollView
                style={{ maxHeight: 300 }}
                showsVerticalScrollIndicator={true}
            >
                <View
                    style={{
                        flexDirection: "row",
                        flexWrap: "wrap",
                        justifyContent: "center",
                        paddingBottom: 16,
                    }}
                >
                    {years.map((year) => (
                        <TouchableOpacity
                            key={year}
                            style={{
                                width: 70,
                                height: 45,
                                margin: 4,
                                padding: 10,
                                borderRadius: 8,
                                backgroundColor:
                                    selectedYear === year
                                        ? "#3b82f6"
                                        : "#f1f5f9",
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                            onPress={() => onYearSelect(year)}
                        >
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontWeight: "500",
                                    color:
                                        selectedYear === year
                                            ? "white"
                                            : "#0f172a",
                                }}
                            >
                                {year}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
        </View>
    );
}
