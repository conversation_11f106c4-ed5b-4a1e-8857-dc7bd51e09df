import React from "react";
import { Tabs } from "expo-router";

import { Ionicons } from "@expo/vector-icons";

const TabsLayout = () => {
    return (
        <Tabs
            screenOptions={{
                headerShown: true,
                tabBarActiveTintColor: "#1d4ed8",
                tabBarInactiveTintColor: "#60a5fa",
                tabBarStyle: {
                    borderTopWidth: 1,
                    borderTopColor: "#e5e7eb", //gray-200 : #e5e7eb
                    elevation: 0,
                    height: 80,
                    paddingBottom: 10,
                },
                tabBarLabelStyle: {
                    fontSize: 12,
                    fontWeight: "500",
                },
                headerStyle: {
                    elevation: 0,
                    shadowOpacity: 0,
                    borderBottomWidth: 1,
                    borderBottomColor: "#fff",
                },
                headerTitleStyle: {
                    fontWeight: "600",
                    fontSize: 18,
                },
            }}
        >
            <Tabs.Screen
                name="home"
                options={{
                    title: "Home",
                    headerShown: false,
                    tabBarIcon: ({ focused }) => (
                        <Ionicons
                            name={focused ? "home" : "home-outline"}
                            size={22}
                            color={focused ? "#2563eb" : "#60a5fa"}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="search"
                options={{
                    title: "Search",
                    headerShown: false,
                    tabBarIcon: ({ focused }) => (
                        <Ionicons
                            name={focused ? "search" : "search-outline"}
                            size={22}
                            color={focused ? "#2563eb" : "#60a5fa"}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="bookings"
                options={{
                    title: "Bookings",
                    headerShown: false,
                    tabBarIcon: ({ focused }) => (
                        <Ionicons
                            name={focused ? "ticket" : "ticket-outline"}
                            size={22}
                            color={focused ? "#2563eb" : "#60a5fa"}
                        />
                    ),
                }}
            />
            <Tabs.Screen
                name="profile"
                options={{
                    title: "Profile",
                    headerShown: false,
                    tabBarIcon: ({ focused }) => (
                        <Ionicons
                            name={focused ? "person" : "person-outline"}
                            size={22}
                            color={focused ? "#2563eb" : "#60a5fa"}
                        />
                    ),
                }}
            />
        </Tabs>
    );
};

export default TabsLayout;
