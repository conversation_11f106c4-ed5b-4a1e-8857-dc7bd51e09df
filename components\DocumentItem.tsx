import React, { useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    ActivityIndicator,
    Platform,
} from "react-native";
import { FileText, ExternalLink, Calendar } from "@/constants/icons";
import * as WebBrowser from "expo-web-browser";
import Animated, { FadeIn } from "react-native-reanimated";

interface DocumentItemProps {
    title: string;
    documentUrl?: string;
    expirationDate?: Date;
}

export const DocumentItem: React.FC<DocumentItemProps> = ({
    title,
    documentUrl,
    expirationDate,
}) => {
    const [loading, setLoading] = useState(false);

    const handleOpenDocument = async () => {
        if (documentUrl) {
            setLoading(true);
            try {
                await WebBrowser.openBrowserAsync(documentUrl);
            } catch (error) {
                console.error("Error opening document:", error);
            } finally {
                setLoading(false);
            }
        }
    };

    const formatDate = (date?: Date) => {
        if (!date) return "N/A";
        return new Date(date).toLocaleDateString();
    };

    const isExpired = expirationDate
        ? new Date(expirationDate) < new Date()
        : false;
    const expiresIn30Days = expirationDate
        ? (new Date(expirationDate).getTime() - new Date().getTime()) /
              (1000 * 60 * 60 * 24) <
          30
        : false;

    const AnimatedComponent = Platform.OS !== "web" ? Animated.View : View;
    const animationProps =
        Platform.OS !== "web" ? { entering: FadeIn.duration(400) } : {};

    return (
        <AnimatedComponent className="mb-3" {...animationProps}>
            <View className="flex-row items-center bg-white rounded-md p-4 border border-gray-200 ">
                <View className="w-11 h-11 rounded-md bg-blue-500/10 justify-center items-center mr-4">
                    <FileText size={24} color="#3B82F6" />
                </View>

                <View className="flex-1">
                    <Text
                        className={`text-base font-medium text-gray-800 ${
                            Platform.OS === "ios" ? "mb-0.5" : ""
                        }`}
                    >
                        {title}
                    </Text>

                    {expirationDate && (
                        <View className="flex-row items-center mt-1">
                            <Calendar
                                size={14}
                                color={
                                    isExpired
                                        ? "#EF4444" // red-500
                                        : expiresIn30Days
                                        ? "#F59E0B" // amber-500
                                        : "#6B7280" // gray-500
                                }
                            />
                            <Text
                                className={`text-xs ml-1 ${
                                    isExpired
                                        ? "text-red-500"
                                        : expiresIn30Days
                                        ? "text-amber-500"
                                        : "text-gray-500"
                                }`}
                            >
                                {isExpired ? "Expired: " : "Expires: "}
                                {formatDate(expirationDate)}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        </AnimatedComponent>
    );
};
