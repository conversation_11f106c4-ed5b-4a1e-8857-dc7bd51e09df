import { Card } from "@/components/ui/card";
import { Select, SelectItem } from "@/components/ui/select";
import {
    Calendar,
    Check,
    Loader2,
    Mail,
    MapPin,
    Phone,
    Plane,
    User2,
} from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import React, { useEffect, useState } from "react";
import { ActivityIndicator, ScrollView, Text, View } from "react-native";
import MinimalistSeatmap from "./MinimalistSeatmap";
import { capitalizeFirstLetter } from "@/utils/dateFormat";

export default function SeatSelection() {
    const { getAvailableSeats, bookingForm, setBookingForm } =
        useBookingStore();

    const [seatmaps, setSeatmaps] = useState<{
        [segmentId: string]: any;
    }>({});

    const [selectedPassenger, setSelectedPassenger] = useState<number>(0);
    const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
    const [cannotSelect, setCannotSelect] = useState<boolean>(false);

    const [selectedSeats, setSelectedSeats] = useState<{
        [passengerIndex: number]: { [segmentId: string]: string };
    }>({});

    const segments = bookingForm.flightInfo.flights.flatMap((flight) =>
        flight.flight.segments.map((segment) => ({
            ...segment,
            flightNumber: flight.flight.flightNumber,
            aircraftModel: flight.flight.aircraftModel,
            service: flight.services,
            seatPrice: flight.seatPrice,
        }))
    );

    const getSegmentDetails = (segmentId: string) => {
        return segments.find((s) => s._id === segmentId);
    };

    // Format date helper
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
        });
    };

    const currentPassenger = bookingForm.passengersDetails[selectedPassenger];

    const canSelect =
        segments.find((s) => s._id === selectedSegment)?.service
            .seatSelection && currentPassenger.passengerType !== "infant";

    const seatPrice = segments.find(
        (s) => s._id === selectedSegment
    )?.seatPrice;

    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    useEffect(() => {
        async function fetchSegmentSeatmap(segmentId: string) {
            const segment = segments.find((s) => s._id === segmentId);
            if (segment) {
                try {
                    const response = await getAvailableSeats(
                        segmentId,
                        segment.departureDate
                    );
                    setSeatmaps((prev) => ({
                        ...prev,
                        [segmentId]: response.data,
                    }));
                } catch (error) {
                    console.error(
                        `Error fetching seatmap for segment ${segmentId}:`,
                        error
                    );
                }
            }
        }
        if (selectedSegment && !seatmaps[selectedSegment]) {
            fetchSegmentSeatmap(selectedSegment);
        }
    }, [selectedSegment, segments, getAvailableSeats, seatmaps]);

    const handleSeatSelect = (segmentId: string, seatNumber: string) => {
        // Reset error message
        setErrorMessage(null);

        // Added this code to check seat class against passenger class
        const seatmap = seatmaps[segmentId];
        const selectedSeat = seatmap?.seats.find(
            (seat: any) => seat.seatNumber === seatNumber
        );

        // Check if the seat class matches the passenger's booked class
        if (
            selectedSeat &&
            currentPassenger.class &&
            selectedSeat.class.toLowerCase() !==
                currentPassenger.class.toLowerCase()
        ) {
            // Prevent selection and show error message
            setErrorMessage(
                `${currentPassenger.class} passengers cannot select ${selectedSeat.class} class seats.`
            );
            return; // Exit the function early
        }

        setSelectedSeats((prev) => {
            const updatedPassengerSeats = {
                ...prev[selectedPassenger],
                [segmentId]: seatNumber,
            };
            return {
                ...prev,
                [selectedPassenger]: updatedPassengerSeats,
            };
        });

        // Get seat price from the segment
        const segment = segments.find((s) => s._id === segmentId);
        const seatPriceForSegment = segment?.seatPrice || 0;
        let additionalPrice = 0;

        const updatedPassengers = bookingForm.passengersDetails.map(
            (passenger, index) => {
                if (index === selectedPassenger) {
                    const existingAssignments = passenger.seatAssignments || [];
                    const assignmentIndex = existingAssignments.findIndex(
                        (a) => a.segmentId === segmentId
                    );

                    let updatedAssignments;

                    if (assignmentIndex >= 0) {
                        // Update the existing assignment without adding extra price.
                        updatedAssignments = existingAssignments.map(
                            (assignment, i) =>
                                i === assignmentIndex
                                    ? { segmentId, seatNumber }
                                    : assignment
                        );
                    } else {
                        // New assignment: add the seat price.
                        updatedAssignments = [
                            ...existingAssignments,
                            { segmentId, seatNumber },
                        ];
                        additionalPrice = seatPriceForSegment;
                    }

                    return {
                        ...passenger,
                        seatAssignments: updatedAssignments,
                    };
                }
                return passenger;
            }
        );

        // Update the booking form without inadvertently stacking the price
        const updatedBookingForm = {
            ...bookingForm,
            paymentInfo: {
                ...bookingForm.paymentInfo,
                totalAmount:
                    bookingForm.paymentInfo.totalAmount + additionalPrice,
            },
            passengersDetails: updatedPassengers,
        };

        setBookingForm(updatedBookingForm);
    };

    return (
        <ScrollView className="flex-1">
            <View className="py-10 px-4">
                {/* Header */}
                <View className="mb-8">
                    <Text className="text-xl font-bold text-center">
                        Airplane Seat Selection
                    </Text>
                </View>

                <View className="gap-8">
                    {/* Passenger Details & Summary */}
                    <Card className=" mb-4 rounded-xl">
                        <View className="p-4 border-b border-gray-100">
                            <Text className="text-base font-semibold">
                                <User2 size={20} color="#3b82f6" /> Passenger
                                Details
                            </Text>
                        </View>
                        <View className="p-4 gap-6">
                            {bookingForm.passengersDetails.length > 1 && (
                                <View className="gap-2">
                                    <Text className="text-sm font-medium text-gray-600">
                                        Select Passenger
                                    </Text>
                                    <Select
                                        selectedValue={String(
                                            selectedPassenger
                                        )}
                                        onValueChange={(val) =>
                                            setSelectedPassenger(Number(val))
                                        }
                                    >
                                        {bookingForm.passengersDetails.map(
                                            (passenger, index) => (
                                                <SelectItem
                                                    key={index}
                                                    label={`${passenger.firstName} ${passenger.lastName}`}
                                                    value={String(index)}
                                                />
                                            )
                                        )}
                                    </Select>
                                </View>
                            )}

                            {/* Passenger Info Card */}
                            <View className="bg-gray-50 rounded-lg p-4 gap-3">
                                <View className="flex-row justify-between items-start">
                                    <View>
                                        <Text className="text-base font-semibold">
                                            {currentPassenger.firstName}{" "}
                                            {currentPassenger.lastName}
                                        </Text>
                                    </View>
                                    <View className="flex-row items-center gap-1 justify-end">
                                        <Calendar size={16} color="#6b7280" />
                                        <Text className="text-xs text-gray-500">
                                            DOB:{" "}
                                            {formatDate(
                                                currentPassenger.dateOfBirth
                                            )}
                                        </Text>
                                    </View>
                                </View>

                                <View className="gap-2">
                                    <View className="flex-row items-center gap-2">
                                        <Mail size={16} color="#6b7280" />
                                        <Text className="text-sm">
                                            {currentPassenger.email}
                                        </Text>
                                    </View>
                                    <View className="flex-row items-center gap-2">
                                        <Phone size={16} color="#6b7280" />
                                        <Text className="text-sm">
                                            {currentPassenger.phone}
                                        </Text>
                                    </View>
                                </View>
                            </View>

                            <View className="h-px bg-gray-200 my-2" />

                            {/* Seat Assignments */}
                            {currentPassenger.seatAssignments &&
                                currentPassenger.seatAssignments.length > 0 && (
                                    <View className="gap-3">
                                        <View className="flex-row items-center gap-2">
                                            <Plane size={16} color="#3b82f6" />
                                            <Text className="text-sm font-medium">
                                                Selected Seats
                                            </Text>
                                        </View>
                                        <View className="gap-2">
                                            {currentPassenger.seatAssignments.map(
                                                (assignment, i) => {
                                                    const segment =
                                                        getSegmentDetails(
                                                            assignment.segmentId
                                                        );
                                                    return (
                                                        <View
                                                            key={i}
                                                            className="bg-blue-50 rounded-lg p-3 flex-row justify-between items-start"
                                                        >
                                                            <View className="gap-1">
                                                                <View className="flex-row items-center gap-2">
                                                                    <Check
                                                                        size={
                                                                            16
                                                                        }
                                                                        color="#22c55e"
                                                                    />
                                                                    <Text className="font-medium">
                                                                        Seat{" "}
                                                                        {
                                                                            assignment.seatNumber
                                                                        }
                                                                    </Text>
                                                                </View>
                                                                <View className="flex-row items-center gap-1">
                                                                    <MapPin
                                                                        size={
                                                                            12
                                                                        }
                                                                        color="#6b7280"
                                                                    />
                                                                    <Text className="text-xs text-gray-500">
                                                                        {
                                                                            segment?.from
                                                                        }{" "}
                                                                        →{" "}
                                                                        {
                                                                            segment?.to
                                                                        }
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            <View className="items-end">
                                                                <Text className="text-xs text-gray-500">
                                                                    Flight{" "}
                                                                    {
                                                                        segment?.flightNumber
                                                                    }
                                                                </Text>
                                                                <Text className="text-xs text-gray-500">
                                                                    {formatDate(
                                                                        segment?.departureDate ||
                                                                            ""
                                                                    )}
                                                                </Text>
                                                                {segment?.seatPrice && (
                                                                    <Text className="text-xs text-gray-500">
                                                                        $
                                                                        {
                                                                            segment?.seatPrice
                                                                        }
                                                                    </Text>
                                                                )}
                                                            </View>
                                                        </View>
                                                    );
                                                }
                                            )}
                                        </View>
                                    </View>
                                )}
                        </View>
                    </Card>

                    {/* Segment Selection & Seatmap */}
                    <Card className="mb-4 rounded-xl">
                        <View className="p-4 border-b border-gray-100">
                            <Text className="text-base font-semibold">
                                <Plane size={20} color="#3b82f6" /> Choose
                                Segment
                            </Text>
                        </View>
                        <View className="p-4">
                            <Select
                                selectedValue={selectedSegment || ""}
                                onValueChange={(val) => setSelectedSegment(val)}
                            >
                                <SelectItem label="Select Segment" value="" />
                                {segments.map((segment) => (
                                    <SelectItem
                                        key={segment._id}
                                        label={`Flight ${
                                            segment.flightNumber
                                        } – ${capitalizeFirstLetter(
                                            segment.from
                                        )} to ${capitalizeFirstLetter(
                                            segment.to
                                        )}`}
                                        value={segment._id}
                                    />
                                ))}
                            </Select>
                            {selectedSegment && (
                                <View className="mt-4">
                                    {seatmaps[selectedSegment] ? (
                                        <>
                                            <MinimalistSeatmap
                                                canSelect={canSelect || false}
                                                seatPrice={seatPrice || 0}
                                                aircraft={
                                                    seatmaps[selectedSegment]
                                                        .aircraft
                                                }
                                                seats={
                                                    seatmaps[selectedSegment]
                                                        .seats
                                                }
                                                selectedSeat={
                                                    selectedSeats[
                                                        selectedPassenger
                                                    ]
                                                        ? selectedSeats[
                                                              selectedPassenger
                                                          ][selectedSegment] ||
                                                          null
                                                        : null
                                                }
                                                onSeatSelect={(
                                                    seatNumber: string
                                                ) =>
                                                    handleSeatSelect(
                                                        selectedSegment,
                                                        seatNumber
                                                    )
                                                }
                                                currentPassengerClass={
                                                    currentPassenger.class
                                                }
                                            />

                                            {errorMessage && (
                                                <View className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                    <Text className="text-red-600 text-sm text-center">
                                                        {errorMessage}
                                                    </Text>
                                                </View>
                                            )}
                                        </>
                                    ) : (
                                        <View className="h-40 items-center justify-center flex-row gap-2">
                                            <ActivityIndicator
                                                size="large"
                                                color="#3b82f6"
                                            />
                                            <Text className="text-sm text-gray-500">
                                                Loading seatmap...
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            )}
                        </View>
                    </Card>
                </View>
            </View>
        </ScrollView>
    );
}
