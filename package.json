{"name": "somexpress-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "github:react-native-community/datetimepicker", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.8", "@tanstack/react-query": "^5.75.1", "axios": "^1.9.0", "axois": "^0.0.1-security", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-clipboard": "^7.1.4", "expo-constants": "~17.1.5", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-linear-gradient": "^14.1.4", "expo-linking": "^7.1.5", "expo-print": "^14.1.4", "expo-router": "^5.0.7", "expo-secure-store": "^14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.2", "react-native": "0.79.2", "react-native-calendars": "^1.1312.0", "react-native-feather": "^1.1.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "3.4.17", "zod": "^3.24.4", "zustand": "^5.0.4", "expo-updates": "~0.28.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/axios": "^0.14.4", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}