import React from "react";
import {
    View,
    Text,
    ScrollView,
    RefreshControl,
    Platform,
    ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ProfileHeader } from "@/components/profileHeader";
import { ProfileSection } from "@/components/ProfileSection";
import { InfoItem } from "@/components/InfoItem";
import { DocumentItem } from "@/components/DocumentItem";
import { StatusBadge } from "@/components/StatusBadge";
import {
    Mail,
    Phone,
    MapPin,
    Calendar,
    FileText,
    Shield,
    User,
    Building,
    Clock,
    Briefcase,
} from "@/constants/icons";

import Animated, { FadeIn } from "react-native-reanimated";
import { useQuery } from "@tanstack/react-query";
import apiServices from "@/services/apiServices";
import { profileFetchResponse } from "@/types/profile";
import capitalizeFirstLetter from "@/utils/utilFunctions";
import { Button } from "@/components/ui/button";
import { useAuthStore } from "@/stores/auth";

function ProfileScreen() {
    const {
        data: profile,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ["profile"],
        queryFn: () =>
            apiServices.getUserProfile() as Promise<profileFetchResponse>,
        refetchOnWindowFocus: false,
    });

    const { logout } = useAuthStore();

    const onRefresh = () => {
        refetch();
    };

    if (!profile) {
        return (
            <View className="flex-1 justify-center items-center bg-gray-50">
                <ActivityIndicator size="large" color="#3B82F6" />
            </View>
        );
    }

    const formatDate = (date: Date) => {
        return new Date(date).toLocaleDateString();
    };

    const AnimatedText = Platform.OS !== "web" ? Animated.Text : Text;
    const animationProps =
        Platform.OS !== "web" ? { entering: FadeIn.duration(400) } : {};

    return (
        <SafeAreaView className="flex-1 bg-gray-50" edges={["left", "right"]}>
            <ProfileHeader profile={profile.user} />

            <ScrollView
                className="flex-1"
                contentContainerClassName="flex-grow pb-8"
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isLoading}
                        onRefresh={onRefresh}
                        tintColor="#3B82F6"
                        colors={["#3B82F6"]}
                    />
                }
            >
                <View className="flex-1 p-4">
                    <ProfileSection
                        title="Contact Information"
                        icon={<User size={18} color="#3B82F6" />}
                        delay={100}
                    >
                        <InfoItem
                            label="Email"
                            value={profile.user.email}
                            icon={<Mail size={16} color="#6B7280" />}
                            copyable
                        />
                        <InfoItem
                            label="Phone"
                            value={profile.user.phoneNumber[0]}
                            icon={<Phone size={16} color="#6B7280" />}
                            copyable
                        />
                        <InfoItem
                            label="Address"
                            value={profile.user.address}
                            icon={<MapPin size={16} color="#6B7280" />}
                        />
                    </ProfileSection>

                    <ProfileSection
                        title="Agency Information"
                        icon={<Building size={18} color="#3B82F6" />}
                        delay={200}
                    >
                        <InfoItem
                            label="Agency Name"
                            value={profile.user.agentName}
                            icon={<Briefcase size={16} color="#6B7280" />}
                        />
                        <InfoItem
                            label="Account Type"
                            value={profile.user.accountType}
                        />
                        <InfoItem
                            label="Super Agent"
                            value={profile.user.isSuperAgent ? "Yes" : "No"}
                        />
                        <InfoItem
                            label="Assigned City"
                            value={capitalizeFirstLetter(
                                profile.user.assignedCityName
                            )}
                            icon={<MapPin size={16} color="#6B7280" />}
                        />
                        <InfoItem
                            label="Member Since"
                            value={formatDate(profile.user.createdAt)}
                            icon={<Calendar size={16} color="#6B7280" />}
                        />
                    </ProfileSection>

                    <ProfileSection
                        title="Verification Status"
                        icon={<Shield size={18} color="#3B82F6" />}
                        delay={300}
                    >
                        <View className="flex-row items-center justify-between my-2">
                            <StatusBadge
                                status={profile.user.documents.status}
                            />
                            <View className="flex-row items-center">
                                <Clock size={14} color="#6B7280" />
                                <Text className="text-xs text-gray-500 ml-1">
                                    {profile.user.verificationDetails.verifiedAt
                                        ? `Verified on: ${formatDate(
                                              profile.user.verificationDetails
                                                  .verifiedAt
                                          )}`
                                        : "Not verified yet"}
                                </Text>
                            </View>
                        </View>

                        {profile.user.verificationDetails.verificationNotes && (
                            <View className="mt-4 p-4 bg-gray-100 rounded-md">
                                <AnimatedText
                                    className="text-sm font-medium text-gray-800 mb-1"
                                    {...animationProps}
                                >
                                    Notes:
                                </AnimatedText>
                                <AnimatedText
                                    className="text-sm text-gray-500"
                                    {...animationProps}
                                >
                                    {
                                        profile.user.verificationDetails
                                            .verificationNotes
                                    }
                                </AnimatedText>
                            </View>
                        )}
                    </ProfileSection>

                    <ProfileSection
                        title="Documents"
                        icon={<FileText size={18} color="#3B82F6" />}
                        delay={400}
                    >
                        <View className="mt-1">
                            <DocumentItem
                                title="Business Document"
                                documentUrl={
                                    profile.user.documents.businessDocument
                                }
                                expirationDate={
                                    profile.user.documents
                                        .businessDocumentExpirationDate
                                }
                            />
                            <DocumentItem
                                title="Personal Document"
                                documentUrl={
                                    profile.user.documents.personalDocument
                                }
                                expirationDate={
                                    profile.user.documents
                                        .personalDocumentExpirationDate
                                }
                            />
                            <DocumentItem
                                title="Application Document"
                                documentUrl={
                                    profile.user.documents.applicationDocument
                                }
                            />
                        </View>
                    </ProfileSection>
                </View>

                <View>
                    <Button
                        variant="primary"
                        className="mx-4 mb-4 py-4"
                        onPress={() => {
                            logout();
                        }}
                    >
                        Logout
                    </Button>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

export default ProfileScreen;
