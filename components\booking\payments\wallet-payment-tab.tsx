import { Loader2, Wallet } from "@/constants/icons"
import { useBookingStore } from "@/stores/booking-store"
import { useWalletStore } from "@/stores/wallet-store"
import { useQuery } from "@tanstack/react-query"
import React from "react"
import { Text, View } from "react-native"

export function WalletPaymentTab({ totalAmount }: { totalAmount: number }) {
  const { getMyWallet } = useWalletStore()

  const { data: walletData, isLoading: isWalletLoading } = useQuery({
    queryKey: ["mywallet"],
    queryFn: getMyWallet,
  })
    const { bookingForm, setBookingForm } = useBookingStore();

  const hasEnoughBalance = (walletData?.balance ?? 0) >= bookingForm?.paymentInfo?.totalAmount;

  return (
    <>
      <View className="rounded-xl p-5 bg-blue-50">
        <View className="flex-row items-center gap-4">
          <View className="bg-blue-100 p-3 rounded-full">
            <Wallet size={24} color="#3b82f6" />
          </View>
          <View>
            <Text className="text-sm font-medium text-gray-500">Available Balance</Text>
            {isWalletLoading ? (
              <View className="flex-row items-center mt-1">
                <Loader2 size={16} color="#3b82f6" className="mr-2 rotate-45" />
                <Text>Loading...</Text>
              </View>
            ) : (
              <Text className="text-2xl font-bold">${walletData?.balance?.toLocaleString()}</Text>
            )}
          </View>
        </View>
      </View>

      {!hasEnoughBalance && bookingForm?.paymentInfo?.totalAmount > 0 && (
        <View className="mt-3 bg-red-100 p-3 rounded-lg">
          <Text className="font-bold text-red-700">Insufficient Balance</Text>
          <Text className="text-red-700">Your wallet balance is insufficient for this transaction.</Text>
        </View>
      )}
    </>
  )
}
