export interface BookingFormDataType {
  flightInfo: {
    details: {
      numberOfPassengers: number;
      flightType: string;
    };
    flights: any[];
  };
  passengersDetails: PassengerDetail[];
  paymentInfo: {
    totalAmount: number;
    taxAmount: number;
    charges: number;
    subTotal: number;
  };
}
interface SeatConfig {
  class: string;
  numberOfSeats: number;
  layout: string;
  features: string[];
}

interface Seat {
  seatNumber: string;
  class: string;
  status: string;
}

export interface SeatmapProps {
  aircraft: {
      model: string;
      registrationName: string;
      totalSeats: number;
      seatConfig: SeatConfig[];
  };
  seats: Seat[];
}
export interface PassengerDetail {
  firstName: string;
  lastName: string;
  passengerType: 'adult' | 'child' | 'infant';
  dateOfBirth: string;
  class: string;
  passportInfo: {
    passportNumber: string;
    expiryDate: string;
    country: string;
  };
  email: string;
  phone: string;
  seatAssignments: any[];
}

export const defaultBookingForm: BookingFormDataType = {
  flightInfo: {
    details: {
      numberOfPassengers: 0,
      flightType: '',
    },
    flights: [],
  },
  passengersDetails: [],
  paymentInfo: {
    totalAmount: 0,
    taxAmount: 0,
    charges: 0,
    subTotal: 0,
  },
};