{"expo": {"name": "SomExpress Airways", "slug": "somexpress", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "somexpress", "userInterfaceStyle": "automatic", "newArchEnabled": true, "privacy": "public", "platforms": ["ios", "android"], "description": "Book flights, manage tickets, and track your journeys with SomExpress Airways. Your complete flight booking solution.", "keywords": ["flight", "booking", "airline", "travel", "tickets", "airways", "flights"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.abdirashidv.somexpress", "buildNumber": "1", "infoPlist": {"CFBundleDisplayName": "SomExpress Airways", "ITSAppUsesNonExemptEncryption": false}, "associatedDomains": ["applinks:somexpressairways.com", "applinks:somexpressairways.com"], "usesAppleSignIn": false}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.abdirashidv.somexpress", "versionCode": 1, "permissions": []}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#2381fe", "textColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "5422e1b2-6fe9-4abf-881b-e05dce0c03e5"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/5422e1b2-6fe9-4abf-881b-e05dce0c03e5"}}}