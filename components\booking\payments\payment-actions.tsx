import React from "react";

import { Button } from "@/components/ui/button";
import { ArrowRight, CreditCard, Wallet } from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import { usePaymentStore } from "@/stores/payment-store";
import { useWalletStore } from "@/stores/wallet-store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";
import { ActivityIndicator, Text, View } from "react-native";
import type { PaymentDetailsFormData } from "../PaymentDetails";

interface PaymentActionsProps {
    activeTab: string;
    isValid: boolean;
    processingPayment: boolean;
    handleSubmit: any; // Using any for simplicity
    setValue: (name: "paymentMethod", value: "wallet" | "cash") => void;
    totalAmount: number;
    setProcessingPayment: (value: boolean) => void;
    onPaymentComplete?: (success: boolean, data?: any) => void;
    isReservationPayment?: boolean;
}

export function PaymentActions({
    activeTab,
    isValid,
    processingPayment,
    handleSubmit,
    setValue,
    totalAmount,
    setProcessingPayment,
    onPaymentComplete,
    isReservationPayment = false,
}: PaymentActionsProps) {
    const { bookingForm, setBookingForm } = useBookingStore();
    const { getMyWallet } = useWalletStore();
    const { paywithwallet, payWithCash, completeReservationPayment } =
        usePaymentStore();
    const queryClient = useQueryClient();
    const { data: walletData } = useQuery({
        queryKey: ["mywallet"],
        queryFn: getMyWallet,
    });

    const hasEnoughBalance = (walletData?.balance ?? 0) >= bookingForm.paymentInfo.totalAmount;

    // Wallet payment mutation for new bookings
    const walletPaymentMutation = useMutation({
        mutationFn: (formData: PaymentDetailsFormData) => {
            const payload = {
                reservationId: formData.reservationId,
                paymentMethod: formData.paymentMethod,
                amount: Number(bookingForm.paymentInfo.totalAmount),
                description: `Flight booking - ${
                    bookingForm.flightInfo.flights
                        .map(
                            (flight) =>
                                `${flight.flight.segments[0].from} to ${flight.flight.segments[0].to}`
                        )
                        .join(", ") || "Unknown"
                }`,
                bookingData: {
                    flightInfo: bookingForm.flightInfo,
                    passengersDetails: bookingForm.passengersDetails,
                    taxAmount: bookingForm.paymentInfo.taxAmount,
                    charges: bookingForm.paymentInfo.charges,
                    paymentCurrency:
                        bookingForm.paymentInfo.paymentCurrency || "USD",
                    bookingStatus: "booked",
                },
            };

            return paywithwallet(payload);
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ["mywallet"] });

            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "confirmed",
                    transactionReference:
                        data.transaction?._id || `REF-${Date.now()}`,
                },
                bookingReference: {
                    bookingId: data.booking?._id,
                    bookingNumber: data.booking?.bookingNumber,
                    PNR: data.booking?.PNR,
                },
                bookingStatus: "booked",
            }));

            if (onPaymentComplete) {
                onPaymentComplete(true, data);
            }
        },
        onError: (error) => {
            console.error("Payment and booking failed:", error);
            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "cancelled",
                },
                bookingStatus: "cancelled",
            }));

            if (onPaymentComplete) {
                onPaymentComplete(false, error);
            }
        },
        onSettled: () => {
            setProcessingPayment(false);
        },
    });

    // Reservation payment mutation (for existing reservations)
    const reservationPaymentMutation = useMutation({
        mutationFn: (formData: PaymentDetailsFormData) => {
            const payload = {
                reservationId: formData.reservationId,
                paymentMethod: formData.paymentMethod,
                amount: Number(formData.totalAmount),
                description: `Payment for reservation ${
                    bookingForm.bookingReference?.PNR ||
                    bookingForm.bookingReference?.bookingNumber
                }`,
            };

            return completeReservationPayment(payload);
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ["mywallet"] });
            queryClient.invalidateQueries({ queryKey: ["bookings"] });

            if (onPaymentComplete) {
                onPaymentComplete(true, data);
            }
        },
        onError: (error) => {
            console.error("Reservation payment failed:", error);

            if (onPaymentComplete) {
                onPaymentComplete(false, error);
            }
        },
        onSettled: () => {
            setProcessingPayment(false);
        },
    });

    // Cash payment mutation for new bookings
    const cashPaymentMutation = useMutation({
        mutationFn: (formData: PaymentDetailsFormData) => {
            const payload = {
                reservationId: formData.reservationId,
                amount: Number(bookingForm.paymentInfo.totalAmount),
                description: `Cash payment for flight booking - ${
                    bookingForm.flightInfo.flights
                        .map(
                            (flight) =>
                                `${flight.flight.segments[0].from} to ${flight.flight.segments[0].to}`
                        )
                        .join(", ") || "Unknown"
                }`,
                bookingData: {
                    flightInfo: bookingForm.flightInfo,
                    passengersDetails: bookingForm.passengersDetails,
                    taxAmount: bookingForm.paymentInfo.taxAmount,
                    charges: bookingForm.paymentInfo.charges,
                    paymentCurrency:
                        bookingForm.paymentInfo.paymentCurrency || "USD",
                    bookingStatus: "booked",
                },
            };

            return payWithCash(payload);
        },
        onSuccess: (data) => {
            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "confirmed",
                    transactionReference:
                        data.transaction?._id || `CASH-REF-${Date.now()}`,
                },
                bookingReference: {
                    bookingId: data.booking?._id,
                    bookingNumber: data.booking?.bookingNumber,
                    PNR: data.booking?.PNR,
                },
                bookingStatus: "booked",
            }));

            if (onPaymentComplete) {
                onPaymentComplete(true, data);
            }
        },
        onError: (error) => {
            console.error("Cash Payment failed:", error);
            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "cancelled",
                },
                bookingStatus: "cancelled",
            }));

            if (onPaymentComplete) {
                onPaymentComplete(false, error);
            }
        },
        onSettled: () => {
            setProcessingPayment(false);
        },
    });

    // Process payment based on selected method - using useCallback to avoid recreation on each render
    const processPayment = useCallback(
        (data: PaymentDetailsFormData) => {
            setProcessingPayment(true);

            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "pending",
                },
                bookingStatus: "pending",
            }));

            // If this is a payment for an existing reservation
            if (isReservationPayment) {
                reservationPaymentMutation.mutate(data);
                return;
            }

            // Otherwise, process as a new booking payment
            if (data.paymentMethod === "wallet") {
                walletPaymentMutation.mutate(data);
            } else if (data.paymentMethod === "cash") {
                cashPaymentMutation.mutate(data);
            }
        },
        [
            setProcessingPayment,
            setBookingForm,
            isReservationPayment,
            reservationPaymentMutation,
            walletPaymentMutation,
            cashPaymentMutation,
        ]
    );

    return (
        <View className="flex-col w-full items-center justify-center mt-4">
            <Button
                className="h-12 bg-blue-400"
                onPress={() => {
                    setValue("paymentMethod", activeTab as "wallet" | "cash");
                    handleSubmit(processPayment)();
                }}
                disabled={
                    !isValid ||
                    processingPayment ||
                    (activeTab === "wallet" && !hasEnoughBalance)
                }
            >
                {processingPayment ? (
                    <View className="flex-row items-center justify-center">
                        <ActivityIndicator
                            size="small"
                            color="white"
                            className="mr-2"
                        />
                        <Text className="text-white font-semibold">
                            Processing...
                        </Text>
                    </View>
                ) : (
                    <View className="flex-row items-center justify-center">
                        {activeTab === "wallet" ? (
                            <Wallet size={20} color="white" className="mr-2" />
                        ) : (
                            <CreditCard
                                size={20}
                                color="white"
                                className="mr-2"
                            />
                        )}
                        <Text className="text-white font-semibold">
                            {isReservationPayment
                                ? "Complete Payment"
                                : "Book Now"}
                        </Text>
                        <ArrowRight size={16} color="white" className="ml-2" />
                    </View>
                )}
            </Button>

            <Text className="text-xs text-gray-500 text-center mt-2">
                {activeTab === "wallet"
                    ? "Instant confirmation via wallet payment"
                    : "Pay at Office for instant booking"}
            </Text>
        </View>
    );
}
