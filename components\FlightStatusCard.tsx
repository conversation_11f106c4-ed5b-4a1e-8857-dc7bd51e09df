import { Calendar, Clock, Plane } from "@/constants/icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface FlightStatusCardProps {
    departure: string;
    arrival: string;
    date: string;
    time: string;
    flightNumber: string;
    status: string;
    airline: string;
    onPress?: () => void;
}

const FlightStatusCard: React.FC<FlightStatusCardProps> = ({
    departure,
    arrival,
    date,
    time,
    flightNumber,
    status,
    airline,
    onPress,
}) => {
    const isOnTime = status === "On Time";

    const getShortenedName = (name: string) => {
        const words = name.split(" ");
        return words[0].length <= 3
            ? name
            : words
                  .map((word) => word.charAt(0).toUpperCase())
                  .join("")
                  .slice(0, 3);
    };

    return (
        <TouchableOpacity
            style={styles.card}
            onPress={onPress}
            activeOpacity={0.7}
        >
            <View style={styles.header}>
                <View style={styles.flightInfo}>
                    <Text style={styles.flightNumber}>{flightNumber}</Text>
                    <Text style={styles.airline}>{airline}</Text>
                </View>
                <View
                    style={[
                        styles.statusContainer,
                        { backgroundColor: isOnTime ? "#dcfce7" : "#f3f4f6" },
                    ]}
                >
                    <Text
                        style={[
                            styles.statusText,
                            { color: isOnTime ? "#16a34a" : "#4b5563" },
                        ]}
                    >
                        {status}
                    </Text>
                </View>
            </View>

            <View style={styles.routeContainer}>
                <View style={styles.locationContainer}>
                    <Text style={styles.locationCode}>
                        {getShortenedName(
                            departure.split("(")[1].split(")")[0]
                        )}
                    </Text>
                    <Text style={styles.locationName}>
                        {departure.split("(")[0].trim()}
                    </Text>
                </View>

                <View style={styles.flightPathContainer}>
                    <View style={styles.dot} />
                    <View style={styles.line} />
                    <Plane size={16} color="#4f46e5" style={styles.planeIcon} />
                    <View style={styles.line} />
                    <View style={styles.dot} />
                </View>

                <View style={styles.locationContainer}>
                    <Text style={styles.locationCode}>
                        {getShortenedName(arrival.split("(")[1].split(")")[0])}
                    </Text>
                    <Text style={styles.locationName}>
                        {arrival.split("(")[0].trim()}
                    </Text>
                </View>
            </View>

            <View style={styles.footer}>
                <View style={styles.infoItem}>
                    <Calendar size={14} color="#6b7280" />
                    <Text style={styles.infoText}>{date}</Text>
                </View>
                <View style={styles.infoItem}>
                    <Clock size={14} color="#6b7280" />
                    <Text style={styles.infoText}>{time}</Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: "#ffffff",
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
    },
    flightInfo: {
        flexDirection: "column",
    },
    flightNumber: {
        fontSize: 16,
        fontWeight: "600",
        color: "#111827",
        marginBottom: 2,
    },
    airline: {
        fontSize: 14,
        color: "#6b7280",
    },
    statusContainer: {
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: "500",
    },
    routeContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
    },
    locationContainer: {
        alignItems: "center",
        width: "30%",
    },
    locationCode: {
        fontSize: 18,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 4,
    },
    locationName: {
        fontSize: 12,
        color: "#6b7280",
        textAlign: "center",
    },
    flightPathContainer: {
        flexDirection: "row",
        alignItems: "center",
        width: "40%",
        justifyContent: "center",
    },
    dot: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: "#4f46e5",
    },
    line: {
        height: 1,
        backgroundColor: "#d1d5db",
        flex: 1,
    },
    planeIcon: {
        transform: [{ rotate: "90deg" }],
        marginHorizontal: 4,
    },
    footer: {
        flexDirection: "row",
        borderTopWidth: 1,
        borderTopColor: "#f3f4f6",
        paddingTop: 12,
    },
    infoItem: {
        flexDirection: "row",
        alignItems: "center",
        marginRight: 16,
    },
    infoText: {
        fontSize: 13,
        color: "#6b7280",
        marginLeft: 6,
    },
});

export default FlightStatusCard;
