export interface Document {
    businessDocument?: string;
    personalDocument?: string;
    documentsSubmitted: boolean;
    documentsSubmittedAt?: Date;
    status: "APPROVED" | "PENDING" | "REJECTED";
    applicationDocument?: string;
    businessDocumentExpirationDate?: Date;
    personalDocumentExpirationDate?: Date;
    personalImage?: string;
    rejectionReason?: string;
}

export interface VerificationDetails {
    verifiedAt?: Date;
    verifiedBy?: string;
    verificationNotes?: string;
}

export interface ProfileData {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    isActive: boolean;
    address: string;
    phoneNumber: string[];
    accountType: string;
    agentName: string;
    documents: Document;
    isVerified: boolean;
    needsDocuments: boolean;
    verificationDetails: VerificationDetails;
    isSuperAgent: boolean;
    assignedCityName: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface profileFetchResponse {
    success: boolean;
    user: ProfileData;
}
