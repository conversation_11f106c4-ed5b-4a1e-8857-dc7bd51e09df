import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import * as SecureStore from "expo-secure-store";
import api, { setupAuthInterceptors } from "../services/api";

interface AuthState {
    token: string | null;
    user: {
        _id: string;
        fullName: string;
        profilePicture: string;
        accountType: string;
    } | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
    setError: (error: string | null) => void;
    login: (email: string, password: string) => Promise<string | undefined>;
    logout: () => void;
}

// Custom storage adapter for SecureStore with better error handling
const secureStorage = {
    getItem: async (name: string) => {
        try {
            const value = await SecureStore.getItemAsync(name);
            if (!value) return null;

            try {
                return JSON.parse(value);
            } catch (e) {
                console.error("Error parsing stored JSON:", e);
                return null;
            }
        } catch (e) {
            console.error("Error retrieving from SecureStore:", e);
            return null;
        }
    },
    setItem: async (name: string, value: any) => {
        try {
            const jsonValue = JSON.stringify(value);
            await SecureStore.setItemAsync(name, jsonValue);
        } catch (e) {
            console.error("Error storing in SecureStore:", e);
        }
    },
    removeItem: async (name: string) => {
        try {
            await SecureStore.deleteItemAsync(name);
        } catch (e) {
            console.error("Error removing from SecureStore:", e);
        }
    },
};

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            token: null,
            isLoading: false,
            error: null,
            isAuthenticated: false,
            user: null,

            setError: (error) => {
                set({ error });
            },

            login: async (email, password) => {
                set({ isLoading: true, error: null });
                try {
                    const response = await api.post("/users/login", {
                        email,
                        password,
                    });

                    const data = response.data;

                    if (data.user.accountType === "ADMIN") {
                        throw new Error(
                            "Admin accounts are not allowed to login from this app."
                        );
                    }

                    set({
                        isAuthenticated: true,
                        token: data.token,
                        isLoading: false,
                        user: {
                            _id: data.user._id,
                            fullName:
                                data.user.firstName + " " + data.user.lastName,
                            profilePicture: data.user.documents.personalImage,
                            accountType: data.user.accountType,
                        },
                    });

                    return data.status;
                } catch (e: any) {
                    set({
                        isAuthenticated: false,
                        error: e.response?.data?.message || e.message,
                        isLoading: false,
                    });
                }
            },

            logout: () => {
                // Clear all auth state properly
                set({
                    token: null,
                    isAuthenticated: false,
                    user: null,
                    error: null,
                    isLoading: false,
                });
            },
        }),
        {
            name: "auth-storage",
            storage: createJSONStorage(() => secureStorage),
            partialize: (state) => ({
                token: state.token,
                user: state.user,
                isAuthenticated: state.isAuthenticated,
            }),
            onRehydrateStorage: () => (state) => {
                if (state) {
                    console.log("Auth state hydrated successfully");
                } else {
                    console.log("Failed to hydrate auth state");
                }
            },
        }
    )
);

// Initialize API interceptors with auth store functions
setupAuthInterceptors(
    () => useAuthStore.getState().token,
    () => useAuthStore.getState().logout()
);
