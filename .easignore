# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies

node_modules/

# Expo

.expo/
dist/
web-build/
expo-env.d.ts

# Native

_.orig._
_.jks
_.p8
_.p12
_.key
\*.mobileprovision

# Metro

.metro-health-check\*

# debug

npm-debug._
yarn-debug._
yarn-error.\*

# macOS

.DS_Store
\*.pem

# local env files

.env\*.local

# typescript

\*.tsbuildinfo

app-example
