import { Stack, useRouter } from "expo-router";
import "./globals.css";
import { useAuthStore } from "@/stores/auth";
import { useEffect, useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { StatusBar } from "expo-status-bar";

const queryClient = new QueryClient();

export default function RootLayout() {
    const { isAuthenticated, user } = useAuthStore();
    const router = useRouter();
    const [isHydrated, setIsHydrated] = useState(false);

    const isLoggedIn = isAuthenticated && user;

    // Check if store is hydrated
    useEffect(() => {
        const checkHydration = async () => {
            // Wait a moment for hydration to complete
            await new Promise((resolve) => setTimeout(resolve, 100));
            setIsHydrated(true);
        };

        checkHydration();
    }, []);

    // Only redirect after hydration is complete
    useEffect(() => {
        if (!isHydrated) return;

        if (isLoggedIn) {
            router.replace("/(tabs)/home");
        } else {
            router.replace("/onboarding");
        }
    }, [isLoggedIn, router, isHydrated]);

    return (
        <QueryClientProvider client={queryClient}>
            <StatusBar style="inverted" />
            <Stack
                screenOptions={{
                    animation: "fade_from_bottom",
                }}
            >
                <Stack.Screen
                    name="onboarding"
                    options={{ headerShown: false, gestureEnabled: false }}
                />
                <Stack.Screen
                    name="login"
                    options={{ headerShown: false, gestureEnabled: false }}
                />
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="booking" options={{ headerShown: false }} />
            </Stack>
        </QueryClientProvider>
    );
}
