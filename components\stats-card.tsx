import { ArrowDownRight, ArrowUpRight } from "@/constants/icons";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface StatsCardProps {
    label: string;
    value: number;
    icon: React.ElementType;
    trend?: number;
    isCurrency?: boolean;
    color?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
    label,
    value,
    icon: Icon,
    trend,
    isCurrency = false,
    color = "#4f46e5",
}) => {
    const formattedValue = isCurrency
        ? `$${value.toLocaleString()}`
        : value.toLocaleString();

    const isPositiveTrend = trend && trend > 0;

    return (
        <View style={styles.card}>
            <View style={styles.header}>
                <View
                    style={[
                        styles.iconContainer,
                        { backgroundColor: `${color}15` },
                    ]}
                >
                    <Icon size={18} color={color} />
                </View>
                {trend !== undefined && (
                    <View
                        style={[
                            styles.trendContainer,
                            {
                                backgroundColor: isPositiveTrend
                                    ? "#dcfce7"
                                    : "#fee2e2",
                            },
                        ]}
                    >
                        {isPositiveTrend ? (
                            <ArrowUpRight size={12} color="#16a34a" />
                        ) : (
                            <ArrowDownRight size={12} color="#dc2626" />
                        )}
                        <Text
                            style={[
                                styles.trendText,
                                {
                                    color: isPositiveTrend
                                        ? "#16a34a"
                                        : "#dc2626",
                                },
                            ]}
                        >
                            {Math.abs(trend)}%
                        </Text>
                    </View>
                )}
            </View>

            <Text style={styles.value}>{formattedValue}</Text>
            <Text style={styles.label}>{label}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: "#ffffff",
        borderRadius: 16,
        padding: 16,
        marginBottom: 12,
        width: "48%",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 12,
    },
    iconContainer: {
        width: 36,
        height: 36,
        borderRadius: 8,
        justifyContent: "center",
        alignItems: "center",
    },
    trendContainer: {
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: 6,
        paddingVertical: 3,
        borderRadius: 12,
    },
    trendText: {
        fontSize: 12,
        fontWeight: "500",
        marginLeft: 2,
    },
    value: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 4,
    },
    label: {
        fontSize: 13,
        color: "#6b7280",
    },
});

export default StatsCard;
