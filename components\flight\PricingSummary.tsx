import React from "react";

import { useEffect, useRef, useState } from "react";
import {
    Animated,
    LayoutAnimation,
    Platform,
    Text,
    TouchableOpacity,
    UIManager,
    View,
} from "react-native";
import { ChevronUp, DollarSign } from "../../constants/icons";
import type { PricingSummary as PricingSummaryType } from "../../types/flight";
import { Card } from "../ui/card";

interface PricingSummaryProps {
    pricingSummary: PricingSummaryType | null;
}

export default function PricingSummary({
    pricingSummary,
}: PricingSummaryProps) {
    const [isExpanded, setIsExpanded] = useState(true);
    const rotateAnim = useRef(new Animated.Value(0)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.95)).current;

    useEffect(() => {
        // Animate icon rotation when expanded state changes
        Animated.timing(rotateAnim, {
            toValue: isExpanded ? 1 : 0,
            duration: 300,
            useNativeDriver: true,
        }).start();

        // Configure the next layout animation
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

        // Fade in animation on mount
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
        }).start();

        // Scale animation on mount
        Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
        }).start();
    }, [isExpanded]);

    // Calculate rotation for the chevron icon
    const rotate = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ["0deg", "180deg"],
    });

    if (!pricingSummary) {
        return (
            <Card className="rounded-xl shadow-md">
                <View className="items-center p-5">
                    <Text className="font-medium text-gray-500">
                        Loading summary...
                    </Text>
                </View>
            </Card>
        );
    }

    return (
        <Animated.View
            style={{ opacity: fadeAnim, transform: [{ scale: scaleAnim }] }}
        >
            <Card className="overflow-hidden rounded-xl border border-gray-200 shadow-md">
                <TouchableOpacity
                    className="flex-row items-center justify-between bg-gradient-to-r from-blue-500 to-blue-600 p-5"
                    onPress={() => setIsExpanded(!isExpanded)}
                    activeOpacity={0.7}
                >
                    <View className="flex-row items-center">
                        <View className="mr-2 h-10 w-10 items-center justify-center rounded-full bg-white">
                            <DollarSign size={20} color="#3B82F6" />
                        </View>
                        <Text className="text-base font-bold ">
                            Price Summary
                        </Text>
                    </View>
                    <View className="flex-row items-center">
                        <Text className="mr-2 text-xl font-bold ">
                            ${pricingSummary.overallCost.toLocaleString()}
                        </Text>
                        <Animated.View style={{ transform: [{ rotate }] }}>
                            <ChevronUp size={20} color="#000" />
                        </Animated.View>
                    </View>
                </TouchableOpacity>

                {isExpanded && (
                    <View className="border-t border-gray-200 bg-white p-5">
                        <View className="mb-3 flex-row justify-between border-b border-gray-200 pb-3">
                            <Text className="text-base font-medium text-gray-700">
                                Base Cost
                            </Text>
                            <Text className="text-base font-bold text-gray-800">
                                ${pricingSummary.baseCost.toLocaleString()}
                            </Text>
                        </View>

                        <View className="mb-3 flex-row justify-between border-b border-gray-200 pb-3">
                            <Text className="text-base font-medium text-gray-700">
                                Tax
                            </Text>
                            <Text className="text-base font-bold text-gray-800">
                                ${pricingSummary.taxCost.toLocaleString()}
                            </Text>
                        </View>

                        {pricingSummary.charges > 0 && (
                            <View className="mb-3 flex-row justify-between">
                                <View className="flex-row items-center">
                                    <Text className="text-base text-gray-400 line-through">
                                        Commission
                                    </Text>
                                    <View className="ml-2 rounded-full bg-red-100 px-2 py-0.5">
                                        <Text className="text-xs font-bold text-red-600">
                                            WAIVED
                                        </Text>
                                    </View>
                                </View>
                                <Text className="text-base text-gray-400 line-through">
                                    ${pricingSummary.charges.toLocaleString()}
                                </Text>
                            </View>
                        )}

                        <View className="mt-1 flex-row justify-between border-t border-gray-200 pt-3">
                            <Text className="text-lg font-bold text-gray-800">
                                Total
                            </Text>
                            <View>
                                <Text className="text-lg font-bold text-blue-600">
                                    $
                                    {pricingSummary.overallCost.toLocaleString()}
                                </Text>
                            </View>
                        </View>
                        <Text className="text-right mt-2 text-xs text-green-600">
                            {pricingSummary.charges > 0
                                ? "Includes commission waiver"
                                : "Best available price"}
                        </Text>
                    </View>
                )}
            </Card>
        </Animated.View>
    );
}
