import {
    Calendar,
    ChevronLeft,
    ChevronRight,
    Clock,
    Plane,
    Search,
    X,
} from "@/constants/icons";
import { images } from "@/constants/images";
import { useBookingStore } from "@/stores/booking-store";
import capitalizeFirstLetter from "@/utils/utilFunctions";
import { useFocusEffect } from "@react-navigation/native";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "expo-router";
import React, { useCallback, useState } from "react";
import {
    ActivityIndicator,
    FlatList,
    Image,
    RefreshControl,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

interface BookingPassenger {
    _id?: string;
    firstName: string;
    lastName: string;
    fullName: string;
    passengerType: string;
    passengerStatus?: string;
}

interface FlightSegment {
    flightNumber: string;
    registrationName: string;
    from: string;
    to: string;
    departureDate: string;
}

interface BookedBy {
    email: string;
    fullName: string;
}

interface Booking {
    _id: string;
    PNR: string;
    bookingNumber: string;
    status: string;
    tripType: string;
    passengers: BookingPassenger[];
    flightSegments: FlightSegment[];
    bookedBy: BookedBy | null;
    customer: BookedBy | null;
    createdAt: string;
    updatedAt: string;
    payment: {
        paymentStatus: string;
    };
}

interface BookingsResponse {
    success: boolean;
    message: string;
    data: {
        bookings: Booking[];
        pagination: {
            total: number;
            currentPage: number;
            totalPages: number;
        };
    };
}

// --- Utilities ---
const formatDate = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
};

const formatTime = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleTimeString(undefined, {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
    });
};

const STATUS_COLORS: Record<string, string> = {
    confirmed: "#16a34a",
    pending: "#eab308",
    cancelled: "#dc2626",
    completed: "#6b7280",
    booked: "#16a34a",
    reserved: "#3b82f6",
};

const BookingCard = ({
    booking,
    onPress,
}: {
    booking: Booking;
    onPress: () => void;
}) => {
    const firstSegment = booking.flightSegments?.[0];
    if (!firstSegment) return null;

    const passengerCount = booking.passengers?.length || 0;
    const tripTypeDisplay =
        booking.tripType === "ROUND_TRIP"
            ? "Round Trip"
            : booking.tripType === "ONE_WAY" || booking.tripType === "One-Way"
            ? "One Way"
            : booking.tripType === "MULTI_CITY"
            ? "Multi-City"
            : "Flight";
    const statusColor =
        STATUS_COLORS[booking.status.toLowerCase()] || "#6b7280";

    return (
        <TouchableOpacity
            className="bg-white rounded-xl p-4 mb-4 shadow-sm shadow-blue-100"
            onPress={onPress}
        >
            <View className="flex-row justify-between items-center mb-4">
                <View className="flex-row items-center">
                    <Image
                        source={images.logo}
                        resizeMode="contain"
                        style={{ width: 32, height: 32 }}
                        className="w-8 h-8 rounded-full mr-3 "
                    />
                    <View>
                        <Text className="text-base font-semibold text-gray-900">
                            {firstSegment.flightNumber.substring(0, 2)}
                        </Text>
                        <Text className="text-sm text-gray-500">
                            Flight {firstSegment.flightNumber}
                        </Text>
                    </View>
                </View>
                <View
                    className="px-2 py-1 rounded-full"
                    style={{ backgroundColor: `${statusColor}20` }}
                >
                    <Text
                        className="text-xs font-medium capitalize"
                        style={{ color: statusColor }}
                    >
                        {booking.status}
                    </Text>
                </View>
            </View>

            <View className="flex-row items-center justify-between mb-4">
                <View className="items-center">
                    <Text className="text-lg font-bold text-gray-900 mb-1">
                        {firstSegment.from?.split(", ")?.[1] ||
                            capitalizeFirstLetter(firstSegment.from)}
                    </Text>
                    <Text className="text-sm text-gray-500 text-center max-w-[100px]">
                        {capitalizeFirstLetter(
                            firstSegment.from?.split(", ")?.[0]
                        ) || ""}
                    </Text>
                </View>

                <View className="flex-1 items-center relative px-2">
                    <View className="h-[1px] bg-gray-200 w-full absolute top-4 translate-y-1/2" />
                    <View className="bg-white p-1 rounded-xl border border-gray-200 rotate-45">
                        <Plane size={16} color="#4f46e5" />
                    </View>
                    <Text className="text-xs text-gray-500 mt-1">
                        {tripTypeDisplay}
                    </Text>
                </View>

                <View className="items-center">
                    <Text className="text-lg font-bold text-gray-900 mb-1">
                        {firstSegment.to?.split(", ")?.[1] ||
                            capitalizeFirstLetter(firstSegment.to)}
                    </Text>
                    <Text className="text-sm text-gray-500 text-center max-w-[100px]">
                        {capitalizeFirstLetter(
                            firstSegment.to?.split(", ")?.[0]
                        ) || ""}
                    </Text>
                </View>
            </View>

            <View className="flex-row justify-between items-center pt-3 border-t border-gray-100">
                <View className="flex-row items-center">
                    <Calendar size={16} color="#6b7280" />
                    <Text className="text-sm text-gray-500 ml-1.5">
                        {formatDate(firstSegment.departureDate)}
                    </Text>
                </View>

                <View className="flex-row items-center">
                    <Clock size={16} color="#6b7280" />
                    <Text className="text-sm text-gray-500 ml-1.5">
                        {formatTime(firstSegment.departureDate)}
                    </Text>
                </View>

                <Text className="text-sm font-medium text-indigo-600">
                    {passengerCount}{" "}
                    {passengerCount === 1 ? "Passenger" : "Passengers"}
                </Text>
            </View>

            {booking.bookingNumber && (
                <View className="flex-row justify-between mt-3 pt-3 border-t border-gray-100">
                    <Text className="text-xs text-gray-500">
                        Booking: {booking.bookingNumber}
                    </Text>
                    {booking.PNR && (
                        <Text className="text-xs text-gray-500 font-medium">
                            PNR: {booking.PNR}
                        </Text>
                    )}
                </View>
            )}
        </TouchableOpacity>
    );
};

export default function Bookings() {
    const router = useRouter();
    const { getBookings } = useBookingStore();

    const [activeTab, setActiveTab] = useState("upcoming");
    const [searchQuery, setSearchQuery] = useState("");
    const [showSearch, setShowSearch] = useState(false);
    const [page, setPage] = useState(1);

    const { data, isLoading, isError, refetch, isFetching } =
        useQuery<BookingsResponse>({
            queryKey: ["bookings", page],
            queryFn: () => getBookings({ page, limit: 10 }),
            staleTime: 5 * 60 * 1000,
        });

    const handleRefresh = () => refetch();

    useFocusEffect(
        useCallback(() => {
            handleRefresh();
        }, [activeTab])
    );

    const now = new Date();
    const allBookings = data?.data.bookings || [];

    const upcomingBookings = allBookings.filter((b) => {
        const dep = b.flightSegments?.[0]?.departureDate;
        return dep && new Date(dep) >= now;
    });

    const pastBookings = allBookings.filter((b) => {
        const dep = b.flightSegments?.[0]?.departureDate;
        return dep && new Date(dep) < now;
    });

    const currentBookings =
        activeTab === "upcoming" ? upcomingBookings : pastBookings;

    const filteredBookings = currentBookings.filter((booking) => {
        const query = searchQuery.toLowerCase();
        const flightNumber =
            booking.flightSegments?.[0]?.flightNumber?.toLowerCase() || "";
        const bookingNumber = booking.bookingNumber?.toLowerCase() || "";
        const pnr = booking.PNR?.toLowerCase() || "";
        const from = booking.flightSegments?.[0]?.from?.toLowerCase() || "";
        const to = booking.flightSegments?.[0]?.to?.toLowerCase() || "";
        const passengerName =
            booking.passengers
                ?.map((p) => p.fullName?.toLowerCase())
                .join(" ") || "";
        return (
            flightNumber.includes(query) ||
            bookingNumber.includes(query) ||
            pnr.includes(query) ||
            from.includes(query) ||
            to.includes(query) ||
            passengerName.includes(query)
        );
    });

    const pagination = data?.data.pagination;

    return (
        <SafeAreaView className="flex-1 bg-gray-50" edges={["top"]}>
            {/* Header */}
            <View className="flex-row justify-between items-center px-4 pt-2 pb-4">
                <Text className="text-2xl font-bold text-gray-900">
                    My Bookings
                </Text>
                <TouchableOpacity
                    className="p-2 rounded-lg bg-gray-100"
                    onPress={() => setShowSearch(!showSearch)}
                >
                    {showSearch ? (
                        <X size={20} color="#4f46e5" />
                    ) : (
                        <Search size={20} color="#4f46e5" />
                    )}
                </TouchableOpacity>
            </View>

            {/* Search */}
            {showSearch && (
                <View className="px-4 pb-4">
                    <TextInput
                        className="bg-white rounded-lg px-4 py-2.5 border border-gray-200 text-base"
                        placeholder="Search bookings..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        autoFocus
                    />
                </View>
            )}

            {/* Tabs */}
            <View className="flex-row px-4 mb-4">
                {["upcoming", "past"].map((tab) => (
                    <TouchableOpacity
                        key={tab}
                        className={`flex-1 py-3 items-center border-b-2 ${
                            activeTab === tab
                                ? "border-indigo-600"
                                : "border-transparent"
                        }`}
                        onPress={() => {
                            setPage(1);
                            setActiveTab(tab);
                        }}
                    >
                        <Text
                            className={`text-base font-medium ${
                                activeTab === tab
                                    ? "text-indigo-600"
                                    : "text-gray-500"
                            }`}
                        >
                            {tab === "upcoming" ? "Upcoming" : "Past"}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Content */}
            {isLoading && !isFetching ? (
                <View className="flex-1 justify-center items-center">
                    <ActivityIndicator size="large" color="#4f46e5" />
                    <Text className="mt-3 text-base text-gray-500">
                        Loading bookings...
                    </Text>
                </View>
            ) : isError ? (
                <View className="flex-1 justify-center items-center px-4">
                    <Text className="text-lg font-semibold text-red-500 mb-2">
                        Error loading bookings
                    </Text>
                    <TouchableOpacity
                        className="bg-indigo-600 px-6 py-3 rounded-lg"
                        onPress={handleRefresh}
                    >
                        <Text className="text-white font-medium">Retry</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <FlatList
                    data={filteredBookings}
                    keyExtractor={(item) => item._id}
                    renderItem={({ item }) => (
                        <BookingCard
                            booking={item}
                            onPress={() =>
                                router.push(`/booking/${item._id}/details`)
                            }
                        />
                    )}
                    contentContainerClassName="px-4 pb-2"
                    refreshControl={
                        <RefreshControl
                            refreshing={isFetching}
                            onRefresh={handleRefresh}
                            colors={["#4f46e5"]}
                            tintColor="#4f46e5"
                        />
                    }
                    ListEmptyComponent={
                        <View className="items-center justify-center py-10">
                            {searchQuery ? (
                                <>
                                    <Search size={48} color="#d1d5db" />
                                    <Text className="text-lg font-semibold text-gray-700 mt-4 mb-2">
                                        No results found
                                    </Text>
                                    <Text className="text-sm text-gray-500 text-center">
                                        No bookings match your search criteria
                                    </Text>
                                </>
                            ) : (
                                <>
                                    <Plane size={48} color="#d1d5db" />
                                    <Text className="text-lg font-semibold text-gray-700 mt-4 mb-2">
                                        No bookings yet
                                    </Text>
                                    <Text className="text-sm text-gray-500 text-center">
                                        Your {activeTab} bookings will appear
                                        here
                                    </Text>
                                </>
                            )}
                        </View>
                    }
                    ListFooterComponent={() => {
                        return pagination && pagination.totalPages > 1 ? (
                            <View className="flex-row justify-between items-center px-4 py-5 bg-white rounded-md border border-gray-200">
                                <TouchableOpacity
                                    className={`p-2 rounded-lg ${
                                        page > 1
                                            ? "bg-indigo-50"
                                            : "bg-gray-100"
                                    }`}
                                    onPress={() =>
                                        page > 1 && setPage((p) => p - 1)
                                    }
                                    disabled={page <= 1}
                                >
                                    <ChevronLeft
                                        size={20}
                                        color={page > 1 ? "#4f46e5" : "#9ca3af"}
                                    />
                                </TouchableOpacity>
                                <Text className="text-sm text-gray-600">
                                    Page {pagination.currentPage} of{" "}
                                    {pagination.totalPages}
                                </Text>
                                <TouchableOpacity
                                    className={`p-2 rounded-lg ${
                                        page < pagination.totalPages
                                            ? "bg-indigo-50"
                                            : "bg-gray-100"
                                    }`}
                                    onPress={() =>
                                        page < pagination.totalPages &&
                                        setPage((p) => p + 1)
                                    }
                                    disabled={page >= pagination.totalPages}
                                >
                                    <ChevronRight
                                        size={20}
                                        color={
                                            page < pagination.totalPages
                                                ? "#4f46e5"
                                                : "#9ca3af"
                                        }
                                    />
                                </TouchableOpacity>
                            </View>
                        ) : null;
                    }}
                />
            )}
        </SafeAreaView>
    );
}
