import api from "./api";

class ApiService {
    async getWalletInfo(userId: string) {
        const response = await api.get(`/wallet/user/${userId}`);
        if (response.status === 200) {
            return response.data;
        } else {
            throw new Error("Failed to fetch wallet information");
        }
    }

    async getUpcomingFlights() {
        const response = await api.get(`/setup/flight/upcoming`);
        if (response.status === 200) {
            return response.data;
        } else {
            throw new Error("Failed to fetch upcoming flights");
        }
    }

    async getUserProfile() {
        const response = await api.get("/users/me");
        if (response.status === 200) {
            return response.data;
        } else {
            throw new Error("Failed to fetch user profile");
        }
    }

    async getAgentStats() {
        const response = await api.get("/bookings/agent-stats");
        if (response.status === 200) {
            return response.data;
        } else {
            throw new Error("Failed to fetch agent stats");
        }
    }
}

export default new ApiService();
