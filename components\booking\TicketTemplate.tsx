import {
    Calendar,
    CheckCircle,
    Clock,
    Mail,
    Phone,
    Plane,
    User,
} from "@/constants/icons";
import { images } from "@/constants/images";
import { Image } from "expo-image";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface TicketTemplateProps {
    booking: any;
}

export default function TicketTemplate({ booking }: TicketTemplateProps) {
    if (!booking) return null;

    const formatDate = (dateString: string) => {
        if (!dateString) return "";
        return new Date(dateString).toLocaleDateString(undefined, {
            year: "numeric",
            month: "short",
            day: "numeric",
        });
    };

    const formatTime = (timeString: string) => {
        if (!timeString) return "";
        return timeString;
    };

    // Get first segment for header info
    const firstSegment = booking.flightSegments[0];

    return (
        <View className="bgred" style={styles.container}>
            
            {/* Header */}
            <View style={styles.header}>
                <View style={styles.headerLeft}>
                    <View style={styles.logoContainer}>
                        <Image source={images.logo} style={styles.logo} />
                    </View>
                    <View style={styles.headerTextContainer}>
                        <Text style={styles.airlineName}>
                            Somexpress Airways
                        </Text>
                        <View style={styles.statusContainer}>
                            <CheckCircle size={16} color="#10b981" />
                            <Text style={styles.statusText}>
                                Booking Confirmed
                            </Text>
                        </View>
                    </View>
                </View>

                <View style={styles.headerRight}>
                    <Text style={styles.pnrLabel}>PNR</Text>
                    <Text style={styles.pnrValue}>{booking.PNR}</Text>
                    <Text style={styles.scanText}>Scan to check in</Text>
                </View>
            </View>

            {/* Booking Info */}
            <View style={styles.bookingInfo}>
                <View style={styles.bookingInfoItem}>
                    <Text style={styles.bookingLabel}>Booking Number</Text>
                    <Text style={styles.bookingValue}>
                        {booking.bookingNumber}
                    </Text>
                </View>
                <View style={styles.bookingInfoItem}>
                    <Text style={styles.bookingLabel}>Trip Type</Text>
                    <Text style={styles.bookingValue}>
                        {booking.tripType === "ROUND_TRIP"
                            ? "Round Trip"
                            : booking.tripType === "ONE_WAY"
                            ? "One Way"
                            : booking.tripType}
                    </Text>
                </View>
                <View style={styles.bookingInfoItem}>
                    <Text style={styles.bookingLabel}>Status</Text>
                    <Text style={[styles.bookingValue, styles.statusConfirmed]}>
                        {booking.status.toUpperCase()}
                    </Text>
                </View>
            </View>

            {/* Flight Info */}
            <View style={styles.flightInfoContainer}>
                <Text style={styles.sectionTitle}>Flight Details</Text>

                {booking.flightSegments.map((segment: any, index: number) => (
                    <View key={index} style={styles.flightSegment}>
                        <View style={styles.flightHeader}>
                            <View style={styles.flightNumberContainer}>
                                <Plane size={16} color="#4f46e5" />
                                <Text style={styles.flightNumber}>
                                    {segment.flight.flightNumber}
                                </Text>
                            </View>
                            <Text style={styles.flightType}>
                                {index === 0 ? "OUTBOUND" : "RETURN"}
                            </Text>
                        </View>

                        <View style={styles.routeContainer}>
                            <View style={styles.locationContainer}>
                                <Text style={styles.locationCode}>
                                    {segment.from.split(", ")[1] ||
                                        segment.from
                                            .substring(0, 3)
                                            .toUpperCase()}
                                </Text>
                                <Text style={styles.locationName}>
                                    {segment.from.split(", ")[0] ||
                                        segment.from}
                                </Text>
                            </View>

                            <View style={styles.flightPathContainer}>
                                <View style={styles.flightPath} />
                                <View style={styles.planeIconContainer}>
                                    <Plane
                                        size={16}
                                        color="#4f46e5"
                                        style={styles.planeIcon}
                                    />
                                </View>
                            </View>

                            <View style={styles.locationContainer}>
                                <Text style={styles.locationCode}>
                                    {segment.to.split(", ")[1] ||
                                        segment.to
                                            .substring(0, 3)
                                            .toUpperCase()}
                                </Text>
                                <Text style={styles.locationName}>
                                    {segment.to.split(", ")[0] || segment.to}
                                </Text>
                            </View>
                        </View>

                        <View style={styles.timeContainer}>
                            <View style={styles.timeItem}>
                                <View style={styles.timeIconContainer}>
                                    <Calendar size={14} color="#6b7280" />
                                </View>
                                <Text style={styles.timeText}>
                                    {formatDate(segment.departureDate)}
                                </Text>
                            </View>
                            <View style={styles.timeItem}>
                                <View style={styles.timeIconContainer}>
                                    <Clock size={14} color="#6b7280" />
                                </View>
                                <Text style={styles.timeText}>
                                    {formatTime(segment.departureTime || "")}
                                </Text>
                            </View>
                        </View>

                        {segment.arrivalDate && (
                            <View style={styles.timeContainer}>
                                <View style={styles.timeItem}>
                                    <View style={styles.timeIconContainer}>
                                        <Calendar size={14} color="#6b7280" />
                                    </View>
                                    <Text style={styles.timeText}>
                                        Arrival:{" "}
                                        {formatDate(segment.arrivalDate)}
                                    </Text>
                                </View>
                                {segment.arrivalTime && (
                                    <View style={styles.timeItem}>
                                        <View style={styles.timeIconContainer}>
                                            <Clock size={14} color="#6b7280" />
                                        </View>
                                        <Text style={styles.timeText}>
                                            {formatTime(segment.arrivalTime)}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        )}

                        {segment.status && (
                            <View style={styles.statusRow}>
                                <Text style={styles.statusLabel}>Status:</Text>
                                <Text style={styles.statusValue}>
                                    {segment.status}
                                </Text>
                            </View>
                        )}
                    </View>
                ))}
            </View>

            {/* Passenger Info */}
            <View style={styles.passengerContainer}>
                <Text style={styles.sectionTitle}>Passenger Information</Text>

                {booking.passengers.map((passenger: any, index: number) => (
                    <View key={index} style={styles.passengerItem}>
                        <View style={styles.passengerHeader}>
                            <View style={styles.passengerNameContainer}>
                                <User size={16} color="#4f46e5" />
                                <Text style={styles.passengerName}>
                                    {passenger.firstName} {passenger.lastName}
                                </Text>
                            </View>
                            <Text style={styles.passengerType}>
                                {passenger.passengerType.toUpperCase()}
                            </Text>
                        </View>

                        {/* Additional passenger details */}
                        {passenger.dateOfBirth && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Date of Birth:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {formatDate(passenger.dateOfBirth)}
                                </Text>
                            </View>
                        )}

                        {passenger.passportInfo?.passportNumber && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Passport:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {passenger.passportInfo.passportNumber}
                                </Text>
                            </View>
                        )}

                        {passenger.passportInfo?.expiryDate && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Passport Expiry:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {formatDate(
                                        passenger.passportInfo.expiryDate
                                    )}
                                </Text>
                            </View>
                        )}

                        {passenger.passportInfo?.country && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Nationality:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {passenger.passportInfo.country}
                                </Text>
                            </View>
                        )}

                        {passenger.email && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>Email:</Text>
                                <Text style={styles.detailValue}>
                                    {passenger.email}
                                </Text>
                            </View>
                        )}

                        {passenger.phone && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>Phone:</Text>
                                <Text style={styles.detailValue}>
                                    {passenger.phone}
                                </Text>
                            </View>
                        )}

                        {passenger.seatAssignments &&
                            passenger.seatAssignments.length > 0 && (
                                <View style={styles.passengerDetail}>
                                    <Text style={styles.detailLabel}>
                                        Seat:
                                    </Text>
                                    <Text style={styles.detailValue}>
                                        {passenger.seatAssignments.map(
                                            (seat: any, i: number) => (
                                                <Text key={i}>
                                                    {booking.flightSegments.find(
                                                        (s: any) =>
                                                            s._id ===
                                                            seat.segmentId
                                                    )?.flight.flightNumber ||
                                                        "Flight"}
                                                    : {seat.seatNumber}
                                                    {i <
                                                    passenger.seatAssignments
                                                        .length -
                                                        1
                                                        ? ", "
                                                        : ""}
                                                </Text>
                                            )
                                        )}
                                    </Text>
                                </View>
                            )}

                        {passenger.frequentFlyerNumber && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Frequent Flyer:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {passenger.frequentFlyerNumber}
                                </Text>
                            </View>
                        )}

                        {passenger.specialRequests && (
                            <View style={styles.passengerDetail}>
                                <Text style={styles.detailLabel}>
                                    Special Requests:
                                </Text>
                                <Text style={styles.detailValue}>
                                    {passenger.specialRequests}
                                </Text>
                            </View>
                        )}
                    </View>
                ))}
            </View>

            {/* Payment Info */}
            {booking.payment && (
                <View style={styles.paymentContainer}>
                    <Text style={styles.sectionTitle}>Payment Details</Text>
                    <View style={styles.paymentDetails}>
                        {booking.payment.subTotal && (
                            <View style={styles.paymentRow}>
                                <Text style={styles.paymentLabel}>
                                    Subtotal
                                </Text>
                                <Text style={styles.paymentValue}>
                                    ${booking.payment.subTotal.toFixed(2)}
                                </Text>
                            </View>
                        )}
                        {booking.payment.taxAmount && (
                            <View style={styles.paymentRow}>
                                <Text style={styles.paymentLabel}>Taxes</Text>
                                <Text style={styles.paymentValue}>
                                    ${booking.payment.taxAmount.toFixed(2)}
                                </Text>
                            </View>
                        )}
                        {booking.payment.charges &&
                            booking.payment.charges > 0 && (
                                <View style={styles.paymentRow}>
                                    <Text style={styles.paymentLabel}>
                                        Additional Charges
                                    </Text>
                                    <Text style={styles.paymentValue}>
                                        ${booking.payment.charges.toFixed(2)}
                                    </Text>
                                </View>
                            )}
                        <View style={styles.paymentDivider} />
                        <View style={styles.paymentRow}>
                            <Text style={styles.paymentTotalLabel}>Total</Text>
                            <Text style={styles.paymentTotalValue}>
                                $
                                {booking.payment.totalAmount?.toFixed(2) ||
                                    "0.00"}
                            </Text>
                        </View>
                        <View style={styles.paymentRow}>
                            <Text style={styles.paymentLabel}>
                                Payment Status
                            </Text>
                            <Text style={styles.paymentStatus}>
                                {booking.payment.paymentStatus?.toUpperCase() ||
                                    "PENDING"}
                            </Text>
                        </View>
                    </View>
                </View>
            )}

            {/* Important Info */}
            <View style={styles.infoContainer}>
                <Text style={styles.infoTitle}>Important Information:</Text>
                <View style={styles.infoBullet}>
                    <View style={styles.bulletPoint} />
                    <Text style={styles.infoText}>
                        Please arrive at the airport at least 2 hours before
                        your scheduled departure time.
                    </Text>
                </View>
                <View style={styles.infoBullet}>
                    <View style={styles.bulletPoint} />
                    <Text style={styles.infoText}>
                        Don't forget to bring a valid photo ID or passport for
                        all passengers.
                    </Text>
                </View>
                <View style={styles.infoBullet}>
                    <View style={styles.bulletPoint} />
                    <Text style={styles.infoText}>
                        Check-in closes 45 minutes before departure for domestic
                        flights and 60 minutes for international flights.
                    </Text>
                </View>
            </View>

            {/* Contact Info */}
            <View style={styles.contactContainer}>
                <Text style={styles.contactTitle}>Need assistance?</Text>
                <View style={styles.contactRow}>
                    <View style={styles.contactItem}>
                        <Phone size={14} color="#4f46e5" />
                        <Text style={styles.contactText}>
                            +252-619-222-773
                        </Text>
                    </View>
                    <View style={styles.contactItem}>
                        <Mail size={14} color="#4f46e5" />
                        <Text style={styles.contactText}>
                            <EMAIL>
                        </Text>
                    </View>
                </View>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
                <Text style={styles.footerText}>
                    © Somexpress Airways {new Date().getFullYear()}
                </Text>
                <Text style={styles.footerSubtext}>
                    Generated on {new Date().toLocaleDateString()} at{" "}
                    {new Date().toLocaleTimeString()}
                </Text>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: "white",
        borderRadius: 12,
        overflow: "hidden",
        
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        padding: 16,
        backgroundColor: "#4f46e5",
    },
    headerLeft: {
        flexDirection: "row",
        alignItems: "center",
    },
    headerRight: {
        alignItems: "center",
    },
    logoContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        overflow: "hidden",
        marginRight: 12,
        backgroundColor: "white",
    },
    logo: {
        width: 40,
        height: 40,
    },
    headerTextContainer: {
        flex: 1,
    },
    airlineName: {
        color: "white",
        fontSize: 18,
        fontWeight: "bold",
        marginBottom: 4,
    },
    statusContainer: {
        flexDirection: "row",
        alignItems: "center",
    },
    statusText: {
        color: "rgba(255, 255, 255, 0.9)",
        fontSize: 14,
        marginLeft: 4,
    },
    pnrLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 12,
    },
    pnrValue: {
        color: "white",
        fontSize: 18,
        fontWeight: "bold",
        marginTop: 2,
    },
    scanText: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 10,
        marginTop: 4,
    },
    bookingInfo: {
        flexDirection: "row",
        padding: 16,
        backgroundColor: "#f9fafb",
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
    },
    bookingInfoItem: {
        flex: 1,
    },
    bookingLabel: {
        fontSize: 12,
        color: "#6b7280",
        marginBottom: 4,
    },
    bookingValue: {
        fontSize: 14,
        fontWeight: "600",
        color: "#111827",
    },
    statusConfirmed: {
        color: "#10b981",
    },
    flightInfoContainer: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 12,
    },
    flightSegment: {
        marginBottom: 16,
        backgroundColor: "#f9fafb",
        borderRadius: 8,
        padding: 12,
    },
    flightHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 12,
    },
    flightNumberContainer: {
        flexDirection: "row",
        alignItems: "center",
    },
    flightNumber: {
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 6,
        color: "#111827",
    },
    flightType: {
        fontSize: 10,
        color: "#6b7280",
        backgroundColor: "#e5e7eb",
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
    },
    routeContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        marginBottom: 12,
    },
    locationContainer: {
        alignItems: "center",
        width: 80,
    },
    locationCode: {
        fontSize: 18,
        fontWeight: "bold",
        color: "#111827",
    },
    locationName: {
        fontSize: 10,
        color: "#6b7280",
        textAlign: "center",
    },
    flightPathContainer: {
        flex: 1,
        height: 20,
        position: "relative",
        alignItems: "center",
        justifyContent: "center",
    },
    flightPath: {
        height: 1,
        backgroundColor: "#d1d5db",
        width: "100%",
        position: "absolute",
    },
    planeIconContainer: {
        backgroundColor: "white",
        borderRadius: 12,
        padding: 4,
        borderWidth: 1,
        borderColor: "#e5e7eb",
        transform: [{ rotate: "45deg" }],
    },
    planeIcon: {
        transform: [{ rotate: "-45deg" }],
    },
    timeContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 8,
    },
    timeItem: {
        flexDirection: "row",
        alignItems: "center",
    },
    timeIconContainer: {
        marginRight: 4,
    },
    timeText: {
        fontSize: 12,
        color: "#6b7280",
    },
    statusRow: {
        flexDirection: "row",
        alignItems: "center",
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: "#e5e7eb",
    },
    statusLabel: {
        fontSize: 12,
        color: "#6b7280",
        marginRight: 4,
    },
    statusValue: {
        fontSize: 12,
        fontWeight: "500",
        color: "#111827",
    },
    passengerContainer: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
    },
    passengerItem: {
        backgroundColor: "#f9fafb",
        borderRadius: 8,
        padding: 12,
        marginBottom: 8,
    },
    passengerHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 8,
    },
    passengerNameContainer: {
        flexDirection: "row",
        alignItems: "center",
    },
    passengerName: {
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 6,
        color: "#111827",
    },
    passengerType: {
        fontSize: 10,
        color: "#6b7280",
        backgroundColor: "#e5e7eb",
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
    },
    passengerDetail: {
        flexDirection: "row",
        marginTop: 4,
    },
    detailLabel: {
        fontSize: 12,
        color: "#6b7280",
        width: 100,
    },
    detailValue: {
        fontSize: 12,
        color: "#111827",
        fontWeight: "500",
        flex: 1,
    },
    paymentContainer: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
    },
    paymentDetails: {
        backgroundColor: "#f9fafb",
        borderRadius: 8,
        padding: 12,
    },
    paymentRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 8,
    },
    paymentLabel: {
        fontSize: 12,
        color: "#6b7280",
    },
    paymentValue: {
        fontSize: 12,
        color: "#111827",
    },
    paymentStatus: {
        fontSize: 12,
        fontWeight: "600",
        color: "#10b981",
    },
    paymentDivider: {
        height: 1,
        backgroundColor: "#e5e7eb",
        marginVertical: 8,
    },
    paymentTotalLabel: {
        fontSize: 14,
        fontWeight: "600",
        color: "#111827",
    },
    paymentTotalValue: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#4f46e5",
    },
    infoContainer: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
        backgroundColor: "#f9fafb",
    },
    infoTitle: {
        fontSize: 14,
        fontWeight: "600",
        color: "#111827",
        marginBottom: 8,
    },
    infoBullet: {
        flexDirection: "row",
        marginBottom: 6,
    },
    bulletPoint: {
        width: 4,
        height: 4,
        borderRadius: 2,
        backgroundColor: "#6b7280",
        marginTop: 6,
        marginRight: 8,
    },
    infoText: {
        fontSize: 12,
        color: "#6b7280",
        flex: 1,
    },
    contactContainer: {
        padding: 16,
        backgroundColor: "#eff6ff",
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
    },
    contactTitle: {
        fontSize: 14,
        fontWeight: "600",
        color: "#111827",
        marginBottom: 8,
    },
    contactRow: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    contactItem: {
        flexDirection: "row",
        alignItems: "center",
    },
    contactText: {
        fontSize: 12,
        color: "#4b5563",
        marginLeft: 6,
    },
    footer: {
        padding: 16,
        alignItems: "center",
    },
    footerText: {
        fontSize: 12,
        fontWeight: "600",
        color: "#4f46e5",
        marginBottom: 4,
    },
    footerSubtext: {
        fontSize: 10,
        color: "#9ca3af",
    },
});
