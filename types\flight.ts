export interface SearchParams {
    from: string;
    to: string;
    date: string;
    passengers: number;
}

export interface SearchResponse {
    success: boolean;
    flights?: Flight[];
    suggestions?: {
        dates?: DateOption[];
    };
    message?: string;
}

export interface DateOption {
    date: string;
    available: boolean;
    isBestDeal?: boolean;
    price?: number | string;
}

export interface Flight {
    _id: string;
    flightNumber: string;
    aircraft: {
        _id: string;
        model: string;
        capacity: number;
    };
    cashPayment: boolean;
    segments: Segment[];
    services: Service[];
}

export interface Segment {
    _id: string;
    route: {
        origin: {
            city: string;
            airport?: string;
        };
        destination: {
            city: string;
            airport?: string;
        };
    };
    schedule: {
        departure: {
            date: string;
            time: string;
        };
        arrival: {
            date: string;
            time: string;
        };
        duration: number;
    };
    status: string;
    cabinOptions: ClassOption[];
}

export interface Service {
    seatSelection: boolean;
    extraBaggage: boolean;
    inFlightMeal: boolean;
    priorityBoarding: boolean;
}

export interface ClassOption {
    class: string;
    availableSeats: number;
    isFull: boolean;
    hasEnoughSeats: boolean;
    pricing: {
        adult: number;
        child: number;
        infant: number;
    };
}

export interface Passengers {
    adult: number;
    child: number;
    infant: number;
}

export interface SelectedFlight {
    flight: Flight;
    class: ClassOption;
    legIndex: number;
}

export interface PricingSummary {
    baseCost: number;
    taxCost: number;
    overallCost: number;
    charges: number;
}

export interface Flight {
    arrival: {
        date: string;
        time: string;
    };
    bookedCount: 0;
    departure: {
        date: string;
        time: string;
    };
    flightNumber: string;
    route: {
        destination: {
            airport: string;
            city: string;
        };
        origin: {
            airport: string;
            city: string;
        };
    };
}

export interface UpcomingFlightResponse {
    message: string;
    success: boolean;
    upcomingFlights: Flight[];
}
