import React, { useEffect, useRef } from "react";
import { Animated, Easing, Text, TouchableOpacity, View } from "react-native";
import { Calendar, Plane } from "../../constants/icons";
import type { ClassOption, Flight, Passengers } from "../../types/flight";
import { Card } from "../ui/card";

interface SelectedFlightSummaryProps {
    flight: Flight;
    classOption: ClassOption;
    passengers: Passengers;
    flightType: string;
    onChangeFlight: () => void;
    onChangeDate: () => void;
}

export function SelectedFlightSummary({
    flight,
    classOption,
    passengers,
    flightType,
    onChangeFlight,
    onChangeDate,
}: SelectedFlightSummaryProps) {
    const segment = flight.segments[0];
    const planePosition = useRef(new Animated.Value(0)).current;
    const cardScale = useRef(new Animated.Value(0.98)).current;

    useEffect(() => {
        // Animate plane moving across the route
        Animated.loop(
            Animated.timing(planePosition, {
                toValue: 1,
                duration: 6000,
                easing: Easing.linear,
                useNativeDriver: true,
            })
        ).start();

        // Animate card scale on mount
        Animated.timing(cardScale, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
        }).start();
    }, []);

    const calculateTotalPrice = (pricing: any): number => {
        return (
            pricing.adult * passengers.adult +
            pricing.child * passengers.child +
            pricing.infant * passengers.infant
        );
    };

    const formatDuration = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}h ${remainingMinutes}m`;
    };

    const capitalizeFirstLetter = (string: string) => {
        return string.charAt(0).toUpperCase() + string.slice(1);
    };

    return (
        <Animated.View
            className="mb-4"
            style={{ transform: [{ scale: cardScale }] }}
        >
            <Card className="overflow-hidden rounded-xl border-2 border-blue-100 shadow-md">
                <View className="flex-row items-center justify-between bg-gradient-to-r from-blue-500 to-blue-600 p-3">
                    <View className="flex-row items-center">
                        <View className="mr-2 rounded-full bg-white px-2 py-1">
                            <Text className="text-xs font-bold text-blue-700">
                                {flightType}
                            </Text>
                        </View>
                        <Text className="text-sm font-medium text-white">
                            Flight {flight.flightNumber}
                        </Text>
                    </View>
                    <View className="flex-row items-center">
                        <Text className="text-sm font-bold text-white">
                            {classOption.class}
                        </Text>
                        {classOption.isFull && (
                            <View className="ml-2 rounded-full border border-red-200 bg-red-100 px-2 py-0.5">
                                <Text className="text-xs font-bold text-red-700">
                                    Full
                                </Text>
                            </View>
                        )}
                    </View>
                </View>

                <View className="p-4">
                    <View className="mb-5 flex-row items-center justify-between">
                        <View className="items-center">
                            <Text className="mb-1 text-xl font-bold text-gray-800">
                                {segment.schedule.departure.time}
                            </Text>
                            <Text className="text-xs font-medium text-gray-700">
                                {capitalizeFirstLetter(
                                    segment.route.origin.city
                                )}
                            </Text>
                            <Text className="mt-1 text-[10px] text-gray-500">
                                {segment.route.origin.airport}
                            </Text>
                        </View>

                        <View className="mx-2 flex-1 items-center">
                            <Text className="mb-4 text-xs font-medium text-gray-500">
                                {formatDuration(segment.schedule.duration)}
                            </Text>
                            <View className="relative w-full flex-row items-center">
                                <View className="h-0.5 flex-1 bg-blue-200"></View>
                                <Animated.View
                                    className="absolute top-[-8px] left-0"
                                    style={{
                                        transform: [
                                            {
                                                translateX:
                                                    planePosition.interpolate({
                                                        inputRange: [0, 1],
                                                        outputRange: [0, 100], // Percentage of width
                                                    }),
                                            },
                                        ],
                                    }}
                                >
                                    <View className="rounded-full border-2 border-blue-500 bg-white p-1">
                                        <Plane
                                            size={12}
                                            color="#3B82F6"
                                            style={{
                                                transform: [
                                                    { rotate: "90deg" },
                                                ],
                                            }}
                                        />
                                    </View>
                                </Animated.View>
                            </View>
                            <Text className="mt-5 text-[10px] text-gray-500">
                                Direct Flight
                            </Text>
                        </View>

                        <View className="items-center">
                            <Text className="mb-1 text-xl font-bold text-gray-800">
                                {segment.schedule.arrival.time}
                            </Text>
                            <Text className="text-xs font-medium text-gray-700">
                                {capitalizeFirstLetter(
                                    segment.route.destination.city
                                )}
                            </Text>
                            <Text className="mt-1 text-[10px] text-gray-500">
                                {segment.route.destination.airport}
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row items-center justify-between border-t border-gray-200 pt-3">
                        <View>
                            <Text className="mb-1 text-xs text-gray-500">
                                Total Price
                            </Text>
                            <Text className="text-lg font-bold text-blue-600">
                                $
                                {calculateTotalPrice(
                                    classOption.pricing
                                ).toLocaleString()}
                            </Text>
                        </View>
                        <View className="flex-row">
                            <TouchableOpacity
                                className="flex-row items-center rounded-lg border border-blue-200 bg-blue-50 px-3 py-2  active:bg-blue-100"
                                onPress={onChangeDate}
                            >
                                <Calendar size={14} color="#3B82F6" />
                                <Text className="ml-1 text-xs font-medium text-blue-600">
                                    Change Date
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Card>
        </Animated.View>
    );
}
