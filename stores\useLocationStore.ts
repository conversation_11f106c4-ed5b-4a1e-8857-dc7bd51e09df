import { create } from 'zustand';
import LocationSetupService from '../services/locationSetup';
import { CitySchema, OrganizedLocation } from '../types/settings';

interface LocationState {
  loading: boolean;
  error: string | null;
  createLocation: (data: OrganizedLocation) => Promise<{ success: boolean; message: string }>;
  updateCountries: (countryName: string, data: CitySchema[]) => Promise<any>;
  deleteLocation: (locationId: string) => Promise<{ success: boolean; message: string }>;
}

export const useLocationStore = create<LocationState>((set) => ({
  loading: false,
  error: null,

  createLocation: async (data: OrganizedLocation) => {
    set({ loading: true, error: null });
    try {
      const response = await LocationSetupService.createLocation(data);
      set({ loading: false });
      return { success: true, message: response.data.message };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create location";
      set({ loading: false, error: errorMessage });
      return { success: false, message: errorMessage };
    }
  },

  updateCountries: async (countryName: string, data: CitySchema[]) => {
    set({ loading: true, error: null });
    try {
      const response = await LocationSetupService.updateCountry(countryName, data);
      set({ loading: false });
      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update country";
      set({ loading: false, error: errorMessage });
      return { success: false, message: errorMessage };
    }
  },

  deleteLocation: async (locationId: string) => {
    set({ loading: true, error: null });
    try {
      const response = await LocationSetupService.deleteLocation(locationId);
      set({ loading: false });
      return { success: true, message: response.data.message };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete location";
      set({ loading: false, error: errorMessage });
      return { success: false, message: errorMessage };
    }
  }
}));