import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { ChevronDown, X } from "../../constants/icons";

interface SimpleDatePickerProps {
    onSelectDate: (date: string) => void;
    minDate?: string;
    initialDate?: string;
}

export default function SimpleDatePicker({ 
    onSelectDate, 
    minDate, 
    initialDate 
}: SimpleDatePickerProps) {
    const [selectedDate, setSelectedDate] = useState(
        initialDate ? new Date(initialDate) : new Date()
    );
    const [month, setMonth] = useState(
        initialDate ? new Date(initialDate) : new Date()
    );
    const [showYearPicker, setShowYearPicker] = useState(false);

    const minDateObj = minDate ? new Date(minDate) : new Date();
    const currentYear = new Date().getFullYear();
    
    // Generate years for the year picker (from min date year to current year + 10)
    const minYear = minDateObj.getFullYear();
    const maxYear = currentYear + 10;
    const years = Array.from(
        { length: maxYear - minYear + 1 },
        (_, i) => minYear + i
    );

    // Get days in month
    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month + 1, 0).getDate();
    };

    // Get day names
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    // Get month names
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    // Generate calendar days
    const generateCalendarDays = () => {
        const year = month.getFullYear();
        const monthIndex = month.getMonth();
        const daysInMonth = getDaysInMonth(year, monthIndex);
        const firstDay = new Date(year, monthIndex, 1).getDay();
        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDay; i++) {
            days.push({ day: "", date: null, disabled: true });
        }

        // Add days of the month
        for (let i = 1; i <= daysInMonth; i++) {
            const currentDate = new Date(year, monthIndex, i);
            const isDisabled =
                currentDate < minDateObj ||
                currentDate.toDateString() === "Invalid Date";

            days.push({
                day: i,
                date: currentDate,
                disabled: isDisabled,
            });
        }

        return days;
    };

    // Handle month navigation
    const goToPreviousMonth = () => {
        const newMonth = new Date(month);
        newMonth.setMonth(newMonth.getMonth() - 1);

        // Don't allow going before the minimum date's month
        if (
            newMonth.getFullYear() < minDateObj.getFullYear() ||
            (newMonth.getFullYear() === minDateObj.getFullYear() &&
                newMonth.getMonth() < minDateObj.getMonth())
        ) {
            return;
        }

        setMonth(newMonth);
    };

    const goToNextMonth = () => {
        const newMonth = new Date(month);
        newMonth.setMonth(newMonth.getMonth() + 1);
        setMonth(newMonth);
    };

    // Handle year selection
    const handleYearSelect = (year: number) => {
        const newDate = new Date(month);
        newDate.setFullYear(year);
        setMonth(newDate);
        setShowYearPicker(false);
    };

    // Handle date selection
    const handleDateSelect = (date: Date | null) => {
        if (!date) return;
        setSelectedDate(date);
    };

    const calendarDays = generateCalendarDays();

    return (
        <View className="p-4">
            {/* Year Picker Modal */}
            {showYearPicker && (
                <View className="absolute left-0 right-0 top-0 z-10 h-full bg-white p-4">
                    <View className="flex-row items-center justify-between mb-4">
                        <Text className="text-lg font-semibold text-gray-800">
                            Select Year
                        </Text>
                        <TouchableOpacity
                            className="p-2 rounded-full bg-gray-100"
                            onPress={() => setShowYearPicker(false)}
                        >
                            <X size={20} color="#000" />
                        </TouchableOpacity>
                    </View>
                    <View className="flex-row flex-wrap justify-center">
                        {years.map((year) => (
                            <TouchableOpacity
                                key={year}
                                className={`m-2 w-20 items-center justify-center rounded-lg p-3 ${
                                    year === month.getFullYear()
                                        ? "bg-blue-500"
                                        : "bg-gray-100"
                                }`}
                                onPress={() => handleYearSelect(year)}
                            >
                                <Text
                                    className={`text-base font-medium ${
                                        year === month.getFullYear()
                                            ? "text-white"
                                            : "text-gray-800"
                                    }`}
                                >
                                    {year}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            )}

            {/* Month and Year Header */}
            <View className="mb-6 flex-row items-center justify-between">
                <TouchableOpacity 
                    className="p-2 rounded-full bg-gray-100" 
                    onPress={goToPreviousMonth}
                >
                    <Text className="text-xl font-semibold text-blue-500">
                        ←
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    className="flex-row items-center bg-gray-100 px-4 py-2 rounded-lg"
                    onPress={() => setShowYearPicker(true)}
                >
                    <Text className="text-lg font-semibold text-gray-800 mr-2">
                        {monthNames[month.getMonth()]} {month.getFullYear()}
                    </Text>
                    <ChevronDown size={16} color="#4b5563" />
                </TouchableOpacity>
                <TouchableOpacity 
                    className="p-2 rounded-full bg-gray-100" 
                    onPress={goToNextMonth}
                >
                    <Text className="text-xl font-semibold text-blue-500">
                        →
                    </Text>
                </TouchableOpacity>
            </View>

            {/* Month Selector */}
            <View className="mb-6 flex-row flex-wrap justify-center">
                {monthNames.map((name, index) => (
                    <TouchableOpacity
                        key={name}
                        className={`m-1 px-3 py-2 rounded-lg ${
                            index === month.getMonth()
                                ? "bg-blue-500"
                                : "bg-gray-100"
                        }`}
                        onPress={() => {
                            const newDate = new Date(month);
                            newDate.setMonth(index);
                            setMonth(newDate);
                        }}
                    >
                        <Text
                            className={`text-sm font-medium ${
                                index === month.getMonth()
                                    ? "text-white"
                                    : "text-gray-700"
                            }`}
                        >
                            {name.substring(0, 3)}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Day Names */}
            <View className="mb-2 flex-row bg-gray-50 rounded-lg p-2">
                {dayNames.map((day) => (
                    <Text
                        key={day}
                        className="flex-1 text-center font-medium text-gray-500"
                    >
                        {day}
                    </Text>
                ))}
            </View>

            {/* Calendar Grid */}
            <View className="flex-row flex-wrap bg-white rounded-lg p-2 shadow-sm">
                {calendarDays.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        className={`w-[14.28%] aspect-square items-center justify-center ${
                            item.disabled ? "opacity-30" : ""
                        }`}
                        onPress={() =>
                            !item.disabled && handleDateSelect(item.date)
                        }
                        disabled={item.disabled}
                    >
                        <View
                            className={`w-10 h-10 rounded-full items-center justify-center ${
                                item.date &&
                                selectedDate &&
                                item.date.toDateString() ===
                                    selectedDate.toDateString()
                                    ? "bg-blue-500"
                                    : item.date && 
                                      item.date.toDateString() === 
                                      new Date().toDateString()
                                    ? "bg-blue-100"
                                    : ""
                            }`}
                        >
                            <Text
                                className={`text-base ${
                                    item.date &&
                                    selectedDate &&
                                    item.date.toDateString() ===
                                        selectedDate.toDateString()
                                        ? "font-semibold text-white"
                                        : item.date && 
                                          item.date.toDateString() === 
                                          new Date().toDateString()
                                        ? "font-medium text-blue-700"
                                        : item.disabled
                                        ? "text-gray-400"
                                        : "text-gray-800"
                                }`}
                            >
                                {item.day}
                            </Text>
                        </View>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Confirm Button */}
            <TouchableOpacity
                className="mt-6 rounded-lg bg-blue-500 py-3.5 shadow-sm"
                onPress={() => {
                    // Format date as YYYY-MM-DD using local date values to ensure correct day
                    const year = selectedDate.getFullYear();
                    const month = String(selectedDate.getMonth() + 1).padStart(
                        2,
                        "0"
                    );
                    const day = String(selectedDate.getDate()).padStart(2, "0");
                    const formattedDate = `${year}-${month}-${day}`;
                    onSelectDate(formattedDate);
                }}
            >
                <Text className="text-center font-semibold text-white">
                    Confirm Date
                </Text>
            </TouchableOpacity>
        </View>
    );
}
