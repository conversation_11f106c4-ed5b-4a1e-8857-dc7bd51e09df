import { create } from 'zustand';
import api from '@/services/api';

export interface Airport {
  airportName: string;
  airportCode: string;
}

export interface City {
  cityName: string;
  cityCode: string;
  airports: Airport[];
}

export interface Location {
  _id: string;
  countryName: string;
  countryCode: string;
  cities: City[];
}

interface LocationState {
  locations: Location[];
  loading: boolean;
  error: string | null;
  fetchLocations: () => Promise<void>;
}

export const useLocationStore = create<LocationState>((set) => ({
  locations: [],
  loading: false,
  error: null,
  
  fetchLocations: async () => {
    set({ loading: true, error: null });
    try {
      const response = await api.get('/setup/location/all');
      set({ locations: response.data, loading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch locations';
      set({ error: errorMessage, loading: false });
    }
  },
}));