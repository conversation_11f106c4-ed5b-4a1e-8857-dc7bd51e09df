import React from "react";
import { ActivityIndicator, Text, TouchableOpacity } from "react-native";
import { Colors } from "../../constants/colors";

interface ButtonProps {
    onPress?: () => void;
    children?: React.ReactNode;
    disabled?: boolean;
    loading?: boolean;
    variant?: "primary" | "secondary" | "outline" | "ghost" | "destructive";
    size?: "small" | "medium" | "large";
    className?: string;
    textClassName?: string;
}

export function Button({
    onPress,
    children,
    disabled = false,
    loading = false,
    variant = "primary",
    size = "medium",
    className = "",
    textClassName = "",
}: ButtonProps) {
    const getButtonStyle = () => {
        let style = "items-center justify-center rounded-lg ";

        // Size styles
        if (size === "small") {
            style += "px-3 py-1.5 ";
        } else if (size === "medium") {
            style += "px-4 py-2.5 ";
        } else if (size === "large") {
            style += "px-6 py-3.5 ";
        }

        // Variant styles
        if (variant === "primary") {
            style += disabled ? "bg-blue-300 " : "bg-blue-600 ";
        } else if (variant === "secondary") {
            style += disabled ? "bg-gray-300 " : "bg-gray-800 ";
        } else if (variant === "outline") {
            style += disabled
                ? "bg-transparent border border-gray-300 "
                : "bg-transparent border border-blue-600 ";
        } else if (variant === "ghost") {
            style += "bg-transparent ";
        } else if (variant === "destructive") {
            style += disabled ? "bg-red-300 " : "bg-red-600 ";
        }

        return style + className;
    };

    const getTextStyle = () => {
        let style = "font-bold ";

        // Size styles
        if (size === "small") {
            style += "text-xs ";
        } else if (size === "medium") {
            style += "text-sm ";
        } else if (size === "large") {
            style += "text-base ";
        }

        // Variant styles
        if (variant === "primary" || variant === "secondary") {
            style += "text-white ";
        } else if (variant === "outline") {
            style += disabled ? "text-gray-400 " : "text-blue-600 ";
        } else if (variant === "ghost") {
            style += disabled ? "text-gray-400 " : "text-blue-600 ";
        }

        return style + textClassName;
    };

    return (
        <TouchableOpacity
            onPress={onPress}
            disabled={disabled || loading}
            className={getButtonStyle()}
            activeOpacity={0.7}
        >
            {loading ? (
                <ActivityIndicator
                    size="small"
                    color={
                        variant === "outline" || variant === "ghost"
                            ? Colors.light.primary
                            : "#fff"
                    }
                />
            ) : typeof children === "string" ? (
                <Text className={getTextStyle()}>{children}</Text>
            ) : (
                children
            )}
        </TouchableOpacity>
    );
}
