import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface ActivityCardProps {
  title: string;
  description: string;
  amount: number;
  date: string;
  icon: React.ElementType;
  iconColor?: string;
}

const ActivityCard: React.FC<ActivityCardProps> = ({
  title,
  description,
  amount,
  date,
  icon: Icon,
  iconColor = '#4f46e5'
}) => {
  const isPositive = amount > 0;
  
  return (
    <View style={styles.card}>
      <View style={[styles.iconContainer, { backgroundColor: `${iconColor}15` }]}>
        <Icon size={18} color={iconColor} />
      </View>
      
      <View style={styles.contentContainer}>
        <View>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
          <Text style={styles.date}>{date}</Text>
        </View>
        
        <Text style={[
          styles.amount,
          { color: isPositive ? '#16a34a' : '#111827' }
        ]}>
          {isPositive ? '+' : ''}{amount.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
          })}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  date: {
    fontSize: 12,
    color: '#9ca3af',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ActivityCard;