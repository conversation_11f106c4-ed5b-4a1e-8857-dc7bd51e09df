import api from "@/services/api"
import { create } from "zustand"

export interface Transaction {
  _id: string
  createdAt: string
  amount: number
  description: string
  reference: string
  walletId: string
  type: "CREDIT" | "DEBIT"
  balanceAfter: number
}

export interface Wallet {
  _id: string
  balance: number
  createdAt: string
  updatedAt: string
  cardNumber: string
  cardHolder: string
  provider: string
  userId?: {
    firstName: string
    lastName: string
    email: string
  }
}

export interface TransferData {
  senderWalletId: string
  amount: number
  description: string
  receiverCardNumber: string
}

export interface TransactionsResponse {
  transactions: Transaction[]
  total: number
  totalPages: number
  currentPage: number
}

export interface WalletsResponse {
  wallets: Wallet[]
  count: number
  total: number
  totalPages: number
  currentPage: number
}

export interface CreditDebitData {
  walletId: string
  amount: number
  description?: string
  reference?: string
}

export interface TransactionReportParams {
  walletId?: string
  startDate?: string
  endDate?: string
  type?: "CREDIT" | "DEBIT"
}

export interface TransactionReportResponse {
  transactions: Transaction[]
  count: number
  summary: {
    totalCredit: number
    totalDebit: number
    netChange: number
  }
}

interface WalletState {
  wallet: Wallet | null
  transactions: Transaction[]
  loading: boolean
  error: string | null

  // User functions
  getMyWallet: () => Promise<Wallet>
  getMytransactions: (params?: { page?: number; limit?: number }) => Promise<TransactionsResponse>
  transferMoney: (data: TransferData) => Promise<TransferData>

  // Admin functions
  getAllWallets: (params?: { page?: number; limit?: number }) => Promise<WalletsResponse>
  getWalletByUserId: (userId: string) => Promise<Wallet>
  getWalletTransactions: (
    walletId: string,
    params?: { page?: number; limit?: number; type?: string },
  ) => Promise<TransactionsResponse>
  creditWallet: (data: CreditDebitData) => Promise<{ wallet: Wallet; transaction: Transaction }>
  debitWallet: (data: CreditDebitData) => Promise<{ wallet: Wallet; transaction: Transaction }>
  generateTransactionReport: (params: TransactionReportParams) => Promise<TransactionReportResponse>
}

export const useWalletStore = create<WalletState>((set, get) => ({
  wallet: null,
  transactions: [],
  loading: false,
  error: null,

  // User functions
  getMyWallet: async () => {
    set({ loading: true, error: null })
    try {
      const response = await api.get("/wallet/my-wallet")
      if (!response.data) {
        throw new Error("Failed to fetch wallet data")
      }
      const wallet = response.data.wallet
      set({ wallet, loading: false })
      return wallet
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while fetching wallet"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  getMytransactions: async (params) => {
    set({ loading: true, error: null })
    try {
      const response = await api.get("/wallet/my-transactions", { params })
      set({ transactions: response.data.transactions, loading: false })
      return response.data
    } catch (error) {
      console.error("Error fetching transactions:", error)
      set({ error: "An error occurred while fetching transactions", loading: false })
      throw new Error("An error occurred while fetching transactions")
    }
  },

  transferMoney: async (data) => {
    set({ loading: true, error: null })
    try {
      const response = await api.post("/wallet/transfer", data)
      if (!response.data) {
        throw new Error("Failed to transfer money")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      console.error("Error transferring money:", error)
      const errorMessage = error instanceof Error ? error.message : "An error occurred while transferring money"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  // Admin functions
  getAllWallets: async (params) => {
    set({ loading: true, error: null })
    try {
      const response = await api.get("/wallet/all", { params })
      if (!response.data) {
        throw new Error("Failed to fetch wallets")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while fetching wallets"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  getWalletByUserId: async (userId) => {
    set({ loading: true, error: null })
    try {
      const response = await api.get(`/wallet/user/${userId}`)
      if (!response.data) {
        throw new Error("Failed to fetch wallet")
      }
      set({ loading: false })
      return response.data.wallet
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while fetching wallet"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  getWalletTransactions: async (walletId, params) => {
    set({ loading: true, error: null })
    try {
      const response = await api.get(`/wallet/${walletId}/transactions`, { params })
      if (!response.data) {
        throw new Error("Failed to fetch transactions")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while fetching transactions"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  creditWallet: async (data) => {
    set({ loading: true, error: null })
    try {
      const response = await api.post("/wallet/credit", data)
      if (!response.data) {
        throw new Error("Failed to credit wallet")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while crediting wallet"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  debitWallet: async (data) => {
    set({ loading: true, error: null })
    try {
      const response = await api.post("/wallet/debit", data)
      if (!response.data) {
        throw new Error("Failed to debit wallet")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while debiting wallet"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },

  generateTransactionReport: async (params) => {
    set({ loading: true, error: null })
    try {
      const response = await api.get("/wallet/report", { params })
      if (!response.data) {
        throw new Error("Failed to generate report")
      }
      set({ loading: false })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while generating report"
      set({ error: errorMessage, loading: false })
      throw new Error(errorMessage)
    }
  },
}))
