import { CitySchema, OrganizedLocation } from '../types/settings';
import api from './api';

const LocationSetupService = {
  getLocations: () => {
    return api.get('/setup/location/all');
  },

  createLocation: (data: OrganizedLocation) => {
    return api.post('/setup/location', data);
  },

  updateCountry: (countryName: string, data: CitySchema[]) => {
    return api.put(`/setup/location/${countryName}`, { cities: data });
  },

  deleteLocation: (locationId: string) => {
    return api.delete(`/setup/location/${locationId}`);
  }

};


export default LocationSetupService;