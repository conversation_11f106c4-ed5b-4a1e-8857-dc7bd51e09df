import { useNavigation, useRouter } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
    ActivityIndicator,
    FlatList,
    Modal,
    SafeAreaView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";
import {
    ArrowLeftRight,
    Calendar,
    ChevronDown,
    MapPin,
    Minus,
    Plus,
    Search,
    Users,
    X,
} from "../../constants/icons";
import { Card } from "../ui/card";
import SimpleDatePicker from "../ui/SimpleDatePicker";
import DateSlider from "./DateSlider";
import { FlightOption } from "./FlightOption";
import { SelectedFlightSummary } from "./FlightSummary";
import PricingSummary from "./PricingSummary";

import { useBookingStore } from "@/stores/booking-store";
import { useFlightStore } from "@/stores/flight-store";
import { capitalizeFirstLetter } from "@/utils/dateFormat";
import { useQuery } from "@tanstack/react-query";
import { LinearGradient } from "expo-linear-gradient";
import { useLocations } from "../../hooks/useLocations";
import type {
    ClassOption,
    DateOption,
    Flight,
    Passengers,
    PricingSummary as PricingSummaryType,
    SelectedFlight,
} from "../../types/flight";

// Define types
type SearchType = "oneway" | "return" | "multicity";
type CurrentStep = "search" | "select" | "calendar" | "book";
type FlightKey = "outbound" | "inbound" | `leg${number}`;

interface Route {
    from: string;
    to: string;
    date: string;
}

export function FlightSearch() {
    const router = useRouter();
    const { searchFlights, fetchSystemSettings, loading, error } =
        useFlightStore();
    const { setBookingForm } = useBookingStore();
    const { data: locationsData, isLoading: locationsLoading } = useLocations();

    const [searchType, setSearchType] = useState<SearchType>("oneway");
    const [currentStep, setCurrentStep] = useState<CurrentStep>("search");
    const [currentLegIndex, setCurrentLegIndex] = useState(0);
    const [routes, setRoutes] = useState<Route[]>([
        { from: "", to: "", date: "" },
    ]);
    const [passengers, setPassengers] = useState<Passengers>({
        adult: 1,
        child: 0,
        infant: 0,
    });
    const [flights, setFlights] = useState<Record<FlightKey, Flight[]>>(
        {} as Record<FlightKey, Flight[]>
    );
    const [selectedFlights, setSelectedFlights] = useState<SelectedFlight[]>(
        []
    );
    const [pricingSummary, setPricingSummary] = useState<PricingSummaryType>({
        baseCost: 0,
        taxCost: 0,
        overallCost: 0,
        charges: 0,
    });
    const [settings, setSettings] = useState<any>({});
    const [dateOptions, setDateOptions] = useState<DateOption[]>([]);
    const [searchError, setSearchError] = useState("");
    const [isChangingDate, setIsChangingDate] = useState(false);

    // Modal states
    const [showFromModal, setShowFromModal] = useState(false);
    const [showToModal, setShowToModal] = useState(false);
    const [showDateModal, setShowDateModal] = useState(false);
    const [showPassengerModal, setShowPassengerModal] = useState(false);
    const [datePickerIndex, setDatePickerIndex] = useState(0);
    const [currentModalIndex, setCurrentModalIndex] = useState(0);
    const [fromSuggestions, setFromSuggestions] = useState<any[]>([]);
    const [toSuggestions, setToSuggestions] = useState<any[]>([]);

    const navigation = useNavigation();
    const getTodayString = () => {
        const today = new Date();
        return today.toISOString().split("T")[0];
    };

    const getFlightKey = (legIndex: number): FlightKey => {
        if (searchType === "return") {
            return legIndex === 0 ? "outbound" : "inbound";
        }
        return `leg${legIndex}` as FlightKey;
    };

    const handleSearchTypeChange = (type: SearchType) => {
        setSearchType(type);
        if (type === "oneway") {
            setRoutes([{ from: "", to: "", date: "" }]);
        } else if (type === "return") {
            setRoutes([
                { from: "", to: "", date: "" },
                { from: "", to: "", date: "" },
            ]);
        } else {
            setRoutes([
                { from: "", to: "", date: "" },
                { from: "", to: "", date: "" },
            ]);
        }

        setSelectedFlights([]);
        setCurrentLegIndex(0);
        setCurrentStep("search");
        setDateOptions([]);
        setFlights({} as Record<FlightKey, Flight[]>);
    };

    const openFromModal = (index: number) => {
        setCurrentModalIndex(index);
        loadSuggestions("from", routes[index].from);
        setShowFromModal(true);
    };

    const openToModal = (index: number) => {
        setCurrentModalIndex(index);
        loadSuggestions("to", routes[index].to);
        setShowToModal(true);
    };

     const loadSuggestions = (field: "from" | "to", value: string) => {
    const normalizedValue = value.toLowerCase().trim();

    const suggestions = locationsData
        ?.flatMap((location:any) =>
            location.cities.flatMap((city:any) =>
                city.airports
                    .filter(
                        (airport:any) =>
                            city.cityName.toLowerCase().includes(normalizedValue) ||
                            city.cityCode.toLowerCase().includes(normalizedValue) ||
                            airport.airportName.toLowerCase().includes(normalizedValue) ||
                            airport.airportCode.toLowerCase().includes(normalizedValue)
                    )
                    .map((airport:any) => ({
                        cityName: city.cityName,
                        cityCode: city.cityCode,
                        airportName: airport.airportName,
                        airportCode: airport.airportCode,
                        label: `${airport.airportName} (${airport.airportCode}) - ${city.cityName}`,
                    }))
            )
        );

    if (field === "from") {
        setFromSuggestions(suggestions || []);
    } else {
        setToSuggestions(suggestions || []);
    }
};

    const handleCitySearch = (field: "from" | "to", text: string) => {
        if (field === "from") {
            setRoutes((prev) => {
                const newRoutes = [...prev];
                newRoutes[currentModalIndex] = {
                    ...newRoutes[currentModalIndex],
                    from: text,
                };
                return newRoutes;
            });
            loadSuggestions("from", text);
        } else {
            setRoutes((prev) => {
                const newRoutes = [...prev];
                newRoutes[currentModalIndex] = {
                    ...newRoutes[currentModalIndex],
                    to: text,
                };
                return newRoutes;
            });
            loadSuggestions("to", text);
        }
    };

    const handleCitySelect = (field: "from" | "to", city: any) => {
        setRoutes((prev) => {
            const newRoutes = [...prev];
            newRoutes[currentModalIndex] = {
                ...newRoutes[currentModalIndex],
                [field]: city.cityName,
            };

            // For return flights, automatically set the return leg's from/to based on outbound
            if (searchType === "return" && currentModalIndex === 0) {
                newRoutes[1] = {
                    ...newRoutes[1],
                    from: field === "to" ? city.cityName : newRoutes[0].to,
                    to: field === "from" ? city.cityName : newRoutes[0].from,
                };
            }
            return newRoutes;
        });

        if (field === "from") {
            setShowFromModal(false);
            // Automatically open "to" modal after selecting "from"
            setTimeout(() => openToModal(currentModalIndex), 300);
        } else {
            setShowToModal(false);
            // Automatically open date picker after selecting "to"
            setTimeout(() => openDateModal(currentModalIndex), 300);
        }
    };

    const openDateModal = (index: number) => {
        setDatePickerIndex(index);
        setShowDateModal(true);
    };

    const handleDateSelect = (date: string) => {
        setRoutes((prev) => {
            const newRoutes = [...prev];
            newRoutes[datePickerIndex] = {
                ...newRoutes[datePickerIndex],
                date,
            };

            // For multi-city, ensure dates are in sequence
            if (
                searchType === "multicity" &&
                datePickerIndex < newRoutes.length - 1
            ) {
                const currentDate = new Date(date);
                for (let i = datePickerIndex + 1; i < newRoutes.length; i++) {
                    const nextDate = newRoutes[i].date
                        ? new Date(newRoutes[i].date)
                        : null;
                    if (nextDate && nextDate <= currentDate) {
                        newRoutes[i] = { ...newRoutes[i], date: "" };
                    }
                }
            }

            return newRoutes;
        });

        setShowDateModal(false);

        // If this is a return flight and we just set the outbound date,
        // automatically open date picker for return date
        if (
            searchType === "return" &&
            datePickerIndex === 0 &&
            !routes[1].date
        ) {
            setTimeout(() => openDateModal(1), 300);
        } else if (currentStep !== "search") {
            // Auto-search when date is changed and we're not in initial search step
            setTimeout(() => handleSearch(datePickerIndex), 300);
        }
    };

    const handleSwapFromTo = (index: number) => {
        setRoutes((prev) => {
            const newRoutes = [...prev];
            const temp = newRoutes[index].from;
            newRoutes[index] = {
                ...newRoutes[index],
                from: newRoutes[index].to,
                to: temp,
            };

            // For return flights, update the return leg when swapping outbound
            if (searchType === "return" && index === 0) {
                newRoutes[1] = {
                    ...newRoutes[1],
                    from: newRoutes[index].to,
                    to: newRoutes[index].from,
                };
            }
            return newRoutes;
        });
    };

    const validateSearch = (legIndex: number): boolean => {
        const pair = routes[legIndex];
        if (!pair?.from || !pair?.to || !pair?.date) {
            setSearchError("Please fill in all required fields");
            return false;
        }

        if (pair.from === pair.to) {
            setSearchError("Origin and destination cities cannot be the same");
            return false;
        }

        const selectedDate = new Date(pair.date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            setSearchError("Selected date cannot be earlier than today");
            return false;
        }

        // Return flight specific validations
        if (searchType === "return" && legIndex === 1) {
            // Validate return date
            const outboundDate = new Date(routes[0].date);
            const returnDate = new Date(pair.date);
            if (returnDate <= outboundDate) {
                setSearchError("Return date must be after departure date");
                return false;
            }

            // Validate return route matches outbound (in reverse)
            if (pair.from !== routes[0].to || pair.to !== routes[0].from) {
                setSearchError(
                    "Return flight route must match the outbound route"
                );
                return false;
            }
        }

        // Multi-city validations
        if (searchType === "multicity") {
            // Validate dates are in sequence
            if (legIndex > 0) {
                const prevDate = new Date(routes[legIndex - 1].date);
                const currentDate = new Date(pair.date);
                if (currentDate <= prevDate) {
                    setSearchError(
                        `Flight ${
                            legIndex + 1
                        } date must be after previous flight`
                    );
                    return false;
                }
            }
        }

        return true;
    };

    const handleSearch = async (legIndex: number = currentLegIndex) => {
        if (!validateSearch(legIndex)) return;

        setSearchError("");

        try {
            const pair = routes[legIndex];
            const totalPassengers =
                passengers.adult + passengers.child + passengers.infant;

            const response = await searchFlights({
                from: pair.from,
                to: pair.to,
                date: pair.date,
                passengers: totalPassengers,
            });

            if (response.success && response.flights?.length) {
                setFlights((prev) => ({
                    ...prev,
                    [getFlightKey(legIndex)]: response.flights || [],
                }));

                if (response.suggestions?.dates?.length) {
                    setDateOptions(response.suggestions.dates);
                }

                setCurrentStep("select");
            } else if (response.suggestions?.dates?.length) {
                setDateOptions(response.suggestions.dates);
                setCurrentStep("calendar");
            } else {
                setSearchError("No flights found for the selected dates.");
                setCurrentStep("calendar");
            }
        } catch (error) {
            setSearchError(
                `Error searching flights: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
            setCurrentStep("calendar");
        } finally {
            setIsChangingDate(false);
        }
    };

    const handleFlightSelect = async (
        flight: Flight,
        classOption: ClassOption
    ) => {
        setSelectedFlights((prev) => {
            const filtered = prev.filter((f) => f.legIndex !== currentLegIndex);
            return [
                ...filtered,
                { flight, class: classOption, legIndex: currentLegIndex },
            ];
        });

        if (searchType === "return" && currentLegIndex === 0) {
            // For return flights, move to the return leg
            setCurrentLegIndex(1);
            if (routes[1].date) {
                await handleSearch(1);
            } else {
                setCurrentStep("calendar");
            }
        } else if (
            searchType === "multicity" &&
            currentLegIndex < routes.length - 1
        ) {
            // For multi-city flights, move to the next leg
            const nextLegIndex = currentLegIndex + 1;
            setCurrentLegIndex(nextLegIndex);

            // If we have a date for the next leg, search for flights
            if (routes[nextLegIndex].date) {
                await handleSearch(nextLegIndex);
            } else {
                // If no date is set for the next leg, go to calendar view
                setCurrentStep("calendar");
            }
        } else {
            // If this is the last leg, go to booking
            setCurrentStep("book");
        }
    };

    const handleChangeFlight = (legIndex: number) => {
        setCurrentLegIndex(legIndex);
        setCurrentStep("select");
        handleSearch(legIndex);
    };

    const handleChangeDate = (legIndex: number) => {
        if (!dateOptions.length) return;
        setCurrentLegIndex(legIndex);
        setIsChangingDate(true);
        setCurrentStep("select");
        handleDateSelect(dateOptions[0].date);
    };

    const handleAddCity = () => {
        if (routes.length < 5) {
            setRoutes((prev) => [...prev, { from: "", to: "", date: "" }]);
        }
    };

    const handleRemoveCity = (index: number) => {
        if (routes.length <= 2) return;

        setRoutes((prev) => {
            const newRoutes = prev.filter((_, i) => i !== index);
            if (currentLegIndex >= newRoutes.length) {
                setCurrentLegIndex(newRoutes.length - 1);
            }
            return newRoutes;
        });

        setSelectedFlights((prev) => {
            const newFlights = prev.filter(
                (flight) => flight.legIndex !== index
            );
            return newFlights.map((flight) => ({
                ...flight,
                legIndex:
                    flight.legIndex > index
                        ? flight.legIndex - 1
                        : flight.legIndex,
            }));
        });
    };

    const handleBooking = () => {
        try {
            const selectedClass = selectedFlights[0]?.class.class || "";

            // Calculate seat prices based on settings
            const seatPrices = selectedFlights.map((selected) => {
                const classType = selected.class.class.toLowerCase();
                const classSettings = settings?.classes?.[classType];

                // Get seat price from settings if seat selection is enabled
                const seatPrice =
                    selected.flight.services[0].seatSelection &&
                    classSettings?.services?.seatSelection?.price
                        ? classSettings.services.seatSelection.price
                        : 0;

                return {
                    className: selected.class.class,
                    price: seatPrice,
                };
            });

            // Transform selected flights for booking form
            const transformedSelectedFlights = selectedFlights.map(
                (selected) => {
                    // Find the seat price for this flight's class
                    const seatPrice =
                        seatPrices.find(
                            (seat) => seat.className === selected.class.class
                        )?.price || 0;

                    return {
                        ...selected,
                        class: {
                            cabinClass: selected.class.class,
                            availableSeats: selected.class.availableSeats,
                            hasEnoughSeats: selected.class.hasEnoughSeats,
                            pricing: {
                                adult: selected.class.pricing.adult,
                                child: selected.class.pricing.child,
                                infant: selected.class.pricing.infant,
                            },
                        },
                        flight: {
                            aircraftModel: selected.flight.aircraft.model,
                            flightNumber: selected.flight.flightNumber,
                            cashPayment: selected.flight.cashPayment,
                            segments: selected.flight.segments.map(
                                (segment) => ({
                                    _id: segment._id,
                                    from: segment.route.origin.city,
                                    to: segment.route.destination.city,
                                    departureDate: combineDateTime(
                                        segment.schedule.departure.date,
                                        segment.schedule.departure.time
                                    ).toISOString(),
                                    arrivalDate: combineDateTime(
                                        segment.schedule.arrival.date,
                                        segment.schedule.arrival.time
                                    ).toISOString(),
                                    departureTime:
                                        segment.schedule.departure.time,
                                    arrivalTime: segment.schedule.arrival.time,
                                    duration: segment.schedule.duration,
                                    status: segment.status,
                                })
                            ),
                        },
                        services: {
                            seatSelection:
                                selected.flight.services[0].seatSelection,
                            baggageAllowance:
                                selected.flight.services[0].extraBaggage,
                            mealService:
                                selected.flight.services[0].inFlightMeal,
                            priorityBoarding:
                                selected.flight.services[0].priorityBoarding,
                        },
                        seatPrice: seatPrice, // Use the calculated seat price
                    };
                }
            );

            setBookingForm({
                flightInfo: {
                    details: {
                        numberOfPassengers: Object.values(passengers).reduce(
                            (a, b) => a + b,
                            0
                        ),
                        flightType:
                            searchType === "oneway"
                                ? "One-Way"
                                : searchType === "return"
                                ? "Return"
                                : "Multi-City",
                    },
                    flights: transformedSelectedFlights,
                },
                passengersDetails: [
                    // Create adult passengers
                    ...Array.from({ length: passengers.adult }, () => ({
                        firstName: "",
                        lastName: "",
                        passengerType: "adult" as const,
                        dateOfBirth: "",
                        class: selectedClass,
                        passportInfo: {
                            passportNumber: "",
                            expiryDate: "",
                            country: "",
                        },
                        email: "",
                        phone: "",
                        seatAssignments: [],
                    })),
                    // Create child passengers
                    ...Array.from({ length: passengers.child }, () => ({
                        firstName: "",
                        lastName: "",
                        passengerType: "child" as const,
                        dateOfBirth: "",
                        passportInfo: {
                            passportNumber: "",
                            expiryDate: "",
                            country: "",
                        },
                        email: "",
                        phone: "",
                        seatAssignments: [],
                    })),
                    // Create infant passengers
                    ...Array.from({ length: passengers.infant }, () => ({
                        firstName: "",
                        lastName: "",
                        passengerType: "infant" as const,
                        dateOfBirth: "",
                        passportInfo: {
                            passportNumber: "",
                            expiryDate: "",
                            country: "",
                        },
                        email: "",
                        phone: "",
                        seatAssignments: [],
                    })),
                ],
                paymentInfo: {
                    totalAmount: pricingSummary.overallCost,
                    taxAmount: pricingSummary.taxCost,
                    charges: pricingSummary.charges,
                    subTotal: pricingSummary.baseCost,
                },
            });

            // Use navigation.navigate instead of router.push
            router.push("/booking/add");
        } catch (error) {
            console.error("Error during booking navigation:", error);
            setSearchError(
                `Error during booking: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    };

    function combineDateTime(dateStr: string, timeStr: string): Date {
        const date = new Date(dateStr);
        const [hours, minutes] = timeStr.split(":").map(Number);
        date.setUTCHours(hours, minutes, 0, 0); // Set UTC hours and minutes
        return date;
    }

    const calculateTotalAmount = useCallback(() => {
        // Calculate the subtotal based on selected flights and passenger counts.
        const subtotal = selectedFlights.reduce((total, selected) => {
            const pricing = selected.class.pricing;
            return (
                total +
                pricing.adult * passengers.adult +
                pricing.child * passengers.child +
                pricing.infant * passengers.infant
            );
        }, 0);

        // Safely retrieve the tax rate. If selectedFlights is empty or settings.taxes is undefined, use 0.
        const taxRate =
            selectedFlights.length > 0 &&
            settings?.taxes &&
            settings.taxes.isEnabled
                ? settings.taxes[
                      selectedFlights[0].class.class.toLowerCase()
                  ] || 0
                : 0;

        // Calculate passenger type charges from class settings
        const passengerCharges = selectedFlights.reduce((total, selected) => {
            const classType = selected.class.class.toLowerCase();
            const classSettings = settings?.classes?.[classType];

            if (!classSettings?.passengerTypeCharges) return total;

            return (
                total +
                ((classSettings.passengerTypeCharges.adult || 0) *
                    passengers.adult +
                    (classSettings.passengerTypeCharges.child || 0) *
                        passengers.child +
                    (classSettings.passengerTypeCharges.infant || 0) *
                        passengers.infant)
            );
        }, 0);

        const taxAmount = taxRate;
        const totalAmount = subtotal + taxAmount;

        return {
            subtotal,
            taxAmount,
            totalAmount,
            passengerCharges,
        };
    }, [selectedFlights, passengers, settings]);

    // no useffcet use react query
    const { data: systemSettings } = useQuery({
        queryKey: ["systemSettings"],
        queryFn: async () => {
            const data = await fetchSystemSettings();
            setSettings(data);
            return data;
        },
    });

    useEffect(() => {
        if (currentStep === "book") {
            const { subtotal, taxAmount, totalAmount, passengerCharges } =
                calculateTotalAmount();

            setPricingSummary({
                baseCost: subtotal,
                taxCost: taxAmount,
                charges: passengerCharges,
                overallCost: totalAmount,
            });
        }
    }, [calculateTotalAmount, currentStep]);

    // Auto-search when date changes and we're not in initial search step
    useEffect(() => {
        if (
            currentStep !== "search" &&
            routes[currentLegIndex]?.date &&
            !showDateModal
        ) {
            handleSearch(currentLegIndex);
        }
    }, [routes[currentLegIndex]?.date]);

    const getTotalPassengers = () => {
        return passengers.adult + passengers.child + passengers.infant;
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
            weekday: "short",
            day: "numeric",
            month: "short",
            year: "numeric",
        });
    };

    // City selection modals
    const renderCityModal = (type: "from" | "to") => {
        const isFromModal = type === "from";
        const visible = isFromModal ? showFromModal : showToModal;
        
        const suggestions = isFromModal ? fromSuggestions : toSuggestions;
        const title = isFromModal ? "Select Origin" : "Select Destination";
        const placeholder = isFromModal
            ? "Search origin city or airport"
            : "Search destination city or airport";
        const currentValue = isFromModal
            ? routes[currentModalIndex]?.from
            : routes[currentModalIndex]?.to;

        return (
            <Modal
                visible={visible}
                animationType="slide"
                transparent={false}
                onRequestClose={() =>
                    isFromModal
                        ? setShowFromModal(false)
                        : setShowToModal(false)
                }
            >
                <SafeAreaView className="flex-1 bg-white">
                    <View className="flex-row items-center justify-between border-b border-gray-200 p-4">
                        <Text className="text-lg font-semibold text-gray-800">
                            {title}
                        </Text>
                        <TouchableOpacity
                            onPress={() =>
                                isFromModal
                                    ? setShowFromModal(false)
                                    : setShowToModal(false)
                            }
                            className="p-1"
                        >
                            <X size={24} color="#000" />
                        </TouchableOpacity>
                    </View>

                    <View className="border-b border-gray-200 p-4">
                        <View className="flex-row items-center rounded-lg bg-gray-100 px-3">
                            <MapPin
                                size={20}
                                color="#3B82F6"
                                className="mr-2"
                            />
                            <TextInput
                                className="flex-1 py-3 text-base"
                                placeholder={placeholder}
                                value={currentValue}
                                onChangeText={(text) =>
                                    handleCitySearch(type, text)
                                }
                                autoFocus
                            />
                            {currentValue ? (
                                <TouchableOpacity
                                    onPress={() => handleCitySearch(type, "")}
                                    className="p-1"
                                >
                                    <X size={16} color="#6B7280" />
                                </TouchableOpacity>
                            ) : null}
                        </View>
                    </View>

                   
                    <FlatList
                        data={suggestions}
                        keyExtractor={(item, i) => `${item.cityCode}-${i}`}
                        renderItem={({ item }) => (
                            <TouchableOpacity
                                className="flex-row items-center px-4 py-4 border-b border-gray-100"
                                onPress={() => handleCitySelect(type, item)}
                                activeOpacity={0.7}
                            >
                               { <View className="h-10 w-10 rounded-full bg-blue-50 items-center justify-center mr-3">
                                    <MapPin size={18} color="#3B82F6" />
                                </View>}
                                <View className="flex-1">
                                    <Text className="text-base font-medium text-gray-800">
                                        {capitalizeFirstLetter(item.cityName)} (
                                        {item.cityCode.toUpperCase()})
                                    </Text>
                                    <Text className="mt-0.5 text-sm text-gray-500">
                                        {item.airportName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        )}
                        ListEmptyComponent={
                            <View className="items-center p-8">
                                <Text className="text-gray-500 text-center">
                                    {currentValue
                                        ? "No matching cities or airports found"
                                        : "Start typing to search for cities or airports"}
                                </Text>
                            </View>
                        }
                    />
                </SafeAreaView>
            </Modal>
        );
    };

    // Date picker modal
    const renderDateModal = () => {
        const today = new Date();
        const minDate = today.toISOString().split("T")[0];

        // For return flights, set min date for return to be after outbound
        let minReturnDate = minDate;
        if (
            searchType === "return" &&
            datePickerIndex === 1 &&
            routes[0].date
        ) {
            minReturnDate = routes[0].date;
        }

        return (
            <Modal
                visible={showDateModal}
                animationType="slide"
                transparent={false}
                onRequestClose={() => setShowDateModal(false)}
            >
                <SafeAreaView className="flex-1 bg-white">
                    <View className="flex-row items-center justify-between border-b border-gray-200 p-4">
                        <Text className="text-lg font-semibold text-gray-800">
                            {searchType === "return" && datePickerIndex === 1
                                ? "Select Return Date"
                                : "Select Departure Date"}
                        </Text>
                        <TouchableOpacity
                            onPress={() => setShowDateModal(false)}
                            className="p-1"
                        >
                            <X size={24} color="#000" />
                        </TouchableOpacity>
                    </View>

                    <SimpleDatePicker
                        onSelectDate={handleDateSelect}
                        minDate={
                            datePickerIndex === 1 ? minReturnDate : minDate
                        }
                        initialDate={routes[datePickerIndex]?.date || ""}
                    />
                </SafeAreaView>
            </Modal>
        );
    };

    // Passenger selection modal
    const renderPassengerModal = () => {
        return (
            <Modal
                visible={showPassengerModal}
                animationType="slide"
                transparent={true}
                onRequestClose={() => setShowPassengerModal(false)}
            >
                <View className="flex-1 items-center justify-center bg-black/50">
                    <View className="w-[90%] rounded-xl bg-white p-4 shadow-lg">
                        <View className="mb-4 flex-row items-center justify-between border-b border-gray-200 pb-3">
                            <Text className="text-lg font-semibold text-gray-800">
                                Select Passengers
                            </Text>
                            <TouchableOpacity
                                onPress={() => setShowPassengerModal(false)}
                            >
                                <X size={20} color="#000" />
                            </TouchableOpacity>
                        </View>

                        {Object.entries(passengers).map(([type, count]) => (
                            <View
                                key={type}
                                className="flex-row items-center justify-between border-b border-gray-200 py-3"
                            >
                                <View>
                                    <Text className="text-base font-medium text-gray-800">
                                        {type === "adult"
                                            ? "Adults"
                                            : type === "child"
                                            ? "Children"
                                            : "Infants"}
                                    </Text>
                                    <Text className="mt-0.5 text-xs text-gray-500">
                                        {type === "adult"
                                            ? "12+ years"
                                            : type === "child"
                                            ? "2-11 years"
                                            : "Under 2 years"}
                                    </Text>
                                </View>
                                <View className="flex-row items-center">
                                    <TouchableOpacity
                                        className={`items-center justify-center rounded-full border ${
                                            (type === "adult" && count <= 1) ||
                                            count <= 0
                                                ? "border-gray-300 bg-gray-100"
                                                : "border-blue-500 bg-blue-50"
                                        } h-8 w-8`}
                                        onPress={() =>
                                            setPassengers((prev) => ({
                                                ...prev,
                                                [type]: Math.max(
                                                    type === "adult" ? 1 : 0,
                                                    count - 1
                                                ),
                                            }))
                                        }
                                        disabled={
                                            (type === "adult" && count <= 1) ||
                                            count <= 0
                                        }
                                    >
                                        <Minus
                                            size={16}
                                            color={
                                                (type === "adult" &&
                                                    count <= 1) ||
                                                count <= 0
                                                    ? "#D1D5DB"
                                                    : "#3B82F6"
                                            }
                                        />
                                    </TouchableOpacity>
                                    <Text className="mx-4 text-base font-semibold text-gray-800">
                                        {count}
                                    </Text>
                                    <TouchableOpacity
                                        className="h-8 w-8 items-center justify-center rounded-full border border-blue-500 bg-blue-50"
                                        onPress={() =>
                                            setPassengers((prev) => ({
                                                ...prev,
                                                [type]: count + 1,
                                            }))
                                        }
                                    >
                                        <Plus size={16} color="#3B82F6" />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ))}

                        <TouchableOpacity
                            className="mt-4 rounded-lg bg-blue-500 py-3"
                            onPress={() => setShowPassengerModal(false)}
                        >
                            <Text className="text-center font-semibold text-white">
                                Apply
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        );
    };

    return (
        <Card className="mb-10 overflow-hidden rounded-xl bg-white shadow-md">
            <View className="border-b border-gray-200 p-4">
                <Text className="text-lg font-bold text-gray-800">
                    {currentStep === "select"
                        ? searchType === "return"
                            ? currentLegIndex === 0
                                ? "Select Outbound Flight"
                                : "Select Return Flight"
                            : `Select Flight ${currentLegIndex + 1} of ${
                                  routes.length
                              }`
                        : "Flight Search"}
                </Text>
            </View>

            <View className="p-4">
                {/* Flight Type Selection */}
                <View className="mb-4 flex-row rounded-lg bg-gray-50 p-1">
                    {(["oneway", "return", "multicity"] as const).map(
                        (type) => (
                            <TouchableOpacity
                                key={type}
                                className={`flex-1 items-center justify-center rounded-md py-2.5 ${
                                    searchType === type
                                        ? "bg-blue-500"
                                        : "bg-transparent"
                                }`}
                                onPress={() => handleSearchTypeChange(type)}
                            >
                                <Text
                                    className={`text-sm font-medium ${
                                        searchType === type
                                            ? "text-white"
                                            : "text-gray-600"
                                    }`}
                                >
                                    {type === "oneway"
                                        ? "One way"
                                        : type === "return"
                                        ? "Round trip"
                                        : "Multi-city"}
                                </Text>
                            </TouchableOpacity>
                        )
                    )}
                </View>

                {/* Route Selection */}
                {routes.map((route, index) => (
                    <View
                        key={index}
                        className={`mb-4 overflow-hidden rounded-2xl bg-white shadow-sm `}
                        style={{
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.05,
                            shadowRadius: 3,
                            elevation: 3,
                        }}
                    >
                        {/* Route header */}
                        <View className="border-b border-gray-100 bg-gray-50 px-4 py-3">
                            <Text className="font-medium text-gray-700">
                                {searchType === "return" && index === 1
                                    ? "Return Flight"
                                    : searchType === "multicity"
                                    ? `Flight ${index + 1}`
                                    : "Departure Flight"}
                            </Text>
                        </View>

                        <View className="p-4">
                            {!(searchType === "return" && index === 1) && (
                                <View className="mb-4">
                                    {/* From field */}
                                    <TouchableOpacity
                                        className="mb-3 flex-row items-center overflow-hidden rounded-xl border border-gray-200 bg-white"
                                        onPress={() => openFromModal(index)}
                                        activeOpacity={0.7}
                                    >
                                        <View className=" p-4">
                                            <MapPin size={20} color="#3B82F6" />
                                        </View>
                                        <View className="flex-1 px-3 py-3.5">
                                            <Text className="text-xs font-medium uppercase text-gray-400">
                                                From
                                            </Text>
                                            <Text
                                                className={`text-base ${
                                                    route.from
                                                        ? "font-medium text-gray-800"
                                                        : "text-gray-400"
                                                }`}
                                            >
                                                {capitalizeFirstLetter(
                                                    route.from
                                                ) ||
                                                    "Select origin city or airport"}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>

                                    {/* To field */}
                                    <TouchableOpacity
                                        className="flex-row items-center overflow-hidden rounded-xl border border-gray-200 bg-white"
                                        onPress={() => openToModal(index)}
                                        activeOpacity={0.7}
                                    >
                                        <View className=" p-4">
                                            <MapPin size={20} color="#3B82F6" />
                                        </View>
                                        <View className="flex-1 px-3 py-3.5">
                                            <Text className="text-xs font-medium uppercase text-gray-400">
                                                To
                                            </Text>
                                            <Text
                                                className={`text-base ${
                                                    route.to
                                                        ? "font-medium text-gray-800"
                                                        : "text-gray-400"
                                                }`}
                                            >
                                                {capitalizeFirstLetter(
                                                    route.to
                                                ) ||
                                                    "Select destination city or airport"}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>

                                    {/* Swap button */}
                                    <TouchableOpacity
                                        className="absolute right-0 top-1/2 z-10 mr-2 -translate-y-1/2 transform rounded-full bg-white p-4"
                                        onPress={() => handleSwapFromTo(index)}
                                        style={{
                                            shadowColor: "#000",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.2,
                                            shadowRadius: 3,
                                            elevation: 4,
                                        }}
                                    >
                                        <LinearGradient
                                            colors={["#FFFFFF", "#ffff"]}
                                            className="h-9 w-9 items-center justify-center rounded-full"
                                        >
                                            <ArrowLeftRight
                                                size={18}
                                                color="#3B82F6"
                                            />
                                        </LinearGradient>
                                    </TouchableOpacity>
                                </View>
                            )}

                            {/* Date field */}
                            <View className="flex-row">
                                <TouchableOpacity
                                    className="flex-1 flex-row items-center overflow-hidden rounded-xl border border-gray-200 bg-white"
                                    onPress={() => openDateModal(index)}
                                    activeOpacity={0.7}
                                >
                                    <View className=" p-4">
                                        <Calendar size={20} color="#3B82F6" />
                                    </View>
                                    <View className="flex-1 px-3 py-3.5">
                                        <Text className="text-xs font-medium uppercase text-gray-400">
                                            {searchType === "return" &&
                                            index === 1
                                                ? "Return Date"
                                                : "Departure Date"}
                                        </Text>
                                        <Text
                                            className={`text-base ${
                                                route.date
                                                    ? "font-medium text-gray-800"
                                                    : "text-gray-400"
                                            }`}
                                        >
                                            {route.date
                                                ? formatDate(route.date)
                                                : "Select date"}
                                        </Text>
                                    </View>
                                </TouchableOpacity>

                                {/* Remove city button for multicity */}
                                {searchType === "multicity" && index > 1 && (
                                    <TouchableOpacity
                                        className="ml-3 items-center justify-center rounded-xl bg-red-50 px-4"
                                        onPress={() => handleRemoveCity(index)}
                                        activeOpacity={0.7}
                                    >
                                        <X size={20} color="#EF4444" />
                                    </TouchableOpacity>
                                )}
                            </View>
                        </View>
                    </View>
                ))}

                {/* Add city button for multicity */}
                {searchType === "multicity" && routes.length < 5 && (
                    <TouchableOpacity
                        className="mb-4 flex-row items-center justify-center rounded-xl border border-dashed border-blue-400 bg-blue-50 py-3.5"
                        onPress={handleAddCity}
                        activeOpacity={0.7}
                    >
                        <Plus size={18} color="#3B82F6" className="mr-2" />
                        <Text className="font-medium text-blue-600">
                            Add Another City
                        </Text>
                    </TouchableOpacity>
                )}

                {/* Passenger Selection */}
                <TouchableOpacity
                    className="mb-4 flex-row items-center rounded-lg border border-gray-300 bg-white px-3 py-2.5"
                    onPress={() => setShowPassengerModal(true)}
                >
                    <Users size={18} color="#3B82F6" className="mr-2" />
                    <Text className="flex-1 text-gray-800">
                        {getTotalPassengers()}{" "}
                        {getTotalPassengers() === 1
                            ? "Passenger"
                            : "Passengers"}
                    </Text>
                    <ChevronDown size={16} color="#6B7280" />
                </TouchableOpacity>

                {/* Search Button */}
                <TouchableOpacity
                    className={`flex-row mb-20 items-center justify-center rounded-lg py-3 ${
                        !routes[0].from ||
                        !routes[0].to ||
                        !routes[0].date ||
                        loading
                            ? "bg-blue-300"
                            : "bg-blue-500"
                    }`}
                    onPress={() => handleSearch(0)}
                    disabled={
                        loading ||
                        !routes[0].from ||
                        !routes[0].to ||
                        !routes[0].date
                    }
                >
                    {loading ? (
                        <ActivityIndicator size="small" color="#fff" />
                    ) : (
                        <Search size={20} color="#fff" />
                    )}
                    <Text className="ml-2 font-semibold text-white">
                        Search Flights
                    </Text>
                </TouchableOpacity>

                {/* Error Message */}
                {searchError ? (
                    <View className="mt-4 rounded-lg bg-red-50 p-3">
                        <Text className="text-center text-red-600">
                            {searchError}
                        </Text>
                    </View>
                ) : null}
            </View>

            {/* Selected Flights Summary */}
            {selectedFlights.length > 0 &&
                !isChangingDate &&
                currentStep !== "search" && (
                    <View className="border-t border-gray-200 p-4">
                        <Text className="mb-3 text-lg font-semibold text-gray-800">
                            Selected Flights
                        </Text>
                        {selectedFlights
                            .sort((a, b) => a.legIndex - b.legIndex)
                            .map((selected) => (
                                <SelectedFlightSummary
                                    key={`${selected.flight.flightNumber}-${selected.legIndex}`}
                                    flight={selected.flight}
                                    classOption={selected.class}
                                    passengers={passengers}
                                    flightType={
                                        searchType === "return"
                                            ? selected.legIndex === 0
                                                ? "Outbound"
                                                : "Return"
                                            : `Flight ${selected.legIndex + 1}`
                                    }
                                    onChangeFlight={() =>
                                        handleChangeFlight(selected.legIndex)
                                    }
                                    onChangeDate={() =>
                                        handleChangeDate(selected.legIndex)
                                    }
                                />
                            ))}
                    </View>
                )}

            {/* Date Slider */}
            {currentStep === "calendar" && dateOptions.length > 0 && (
                <DateSlider
                    dates={dateOptions}
                    selectedDate={routes[currentLegIndex].date}
                    onDateSelect={handleDateSelect}
                />
            )}

            {/* Flight Selection */}
            {currentStep === "select" &&
                flights[getFlightKey(currentLegIndex)]?.length > 0 && (
                    <View className="border-t border-gray-200 p-4">
                        <Text className="mb-3 text-lg font-semibold text-gray-800">
                            {searchType === "return"
                                ? currentLegIndex === 0
                                    ? "Select Outbound Flight"
                                    : "Select Return Flight"
                                : `Select Flight ${currentLegIndex + 1} of ${
                                      routes.length
                                  }`}
                        </Text>
                        {/* Use a regular View instead of FlatList to avoid nesting VirtualizedLists */}
                        <View>
                            {flights[getFlightKey(currentLegIndex)].map(
                                (item) => (
                                    <FlightOption
                                        key={item.flightNumber}
                                        flight={item}
                                        onSelect={(classOption) =>
                                            handleFlightSelect(
                                                item,
                                                classOption
                                            )
                                        }
                                        selectedClass={
                                            selectedFlights.find(
                                                (sf) =>
                                                    sf.legIndex ===
                                                    currentLegIndex
                                            )?.class?.class
                                        }
                                        passengers={passengers}
                                        dateOptions={dateOptions}
                                        selectedDate={
                                            routes[currentLegIndex].date
                                        }
                                        onDateSelect={handleDateSelect}
                                        onSearch={handleSearch}
                                        isSearching={loading}
                                    />
                                )
                            )}
                        </View>
                    </View>
                )}

            {/* Booking Summary */}
            {currentStep === "book" && selectedFlights.length > 0 && (
                <View className="border-t border-gray-200 p-4">
                    <View className="mb-4">
                        <PricingSummary pricingSummary={pricingSummary} />
                    </View>

                    <TouchableOpacity
                        className={`rounded-lg py-3 ${
                            (searchType === "return" &&
                                selectedFlights.length !== 2) ||
                            (searchType === "oneway" &&
                                selectedFlights.length !== 1) ||
                            (searchType === "multicity" &&
                                selectedFlights.length !== routes.length)
                                ? "bg-blue-300"
                                : "bg-blue-500"
                        }`}
                        onPress={handleBooking}
                        disabled={
                            (searchType === "return" &&
                                selectedFlights.length !== 2) ||
                            (searchType === "oneway" &&
                                selectedFlights.length !== 1) ||
                            (searchType === "multicity" &&
                                selectedFlights.length !== routes.length)
                        }
                    >
                        <Text className="text-center font-semibold text-white">
                            Book Flights
                        </Text>
                    </TouchableOpacity>
                </View>
            )}

            {/* Modals */}
            {renderCityModal("from")}
            {renderCityModal("to")}
            {renderDateModal()}
            {renderPassengerModal()}
        </Card>
    );
}
