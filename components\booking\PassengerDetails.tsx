import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectItem } from "@/components/ui/select";
import { Calendar, Globe, Passport, User } from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import {
    Modal,
    Platform,
    Pressable,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { z } from "zod";
import DatePicker from "../ui/date-picker";

interface DatePickerProps {
    date: Date | string;
    setDate: (date: Date) => void;
    label?: string;
    placeholder?: string;
    startYear?: number;
    endYear?: number;
    style?: object;
}

// Validation schema for passport information
const passportInfoSchema = z.object({
    passportNumber: z
        .string()
        .min(6, "Passport number must be at least 6 characters")
        .max(12, "Passport number cannot exceed 12 characters")
        .regex(
            /^[a-zA-Z0-9]+$/,
            "Passport number can only contain letters and numbers"
        ),
    expiryDate: z.string().refine((date) => {
        const expiry = new Date(date);
        const today = new Date();
        return expiry > today;
    }, "Passport must not be expired"),
    country: z
        .string()
        .min(2, "Nationality must be at least 2 characters")
        .regex(
            /^[a-zA-Z\s]+$/,
            "Nationality can only contain letters and spaces"
        )
        .max(50, "Nationality cannot exceed 50 characters"),
});

// Validation schema for a single passenger
const passengerSchema = z.object({
    firstName: z.string().min(2, "First name must be at least 2 characters"),
    lastName: z.string().min(2, "Last name must be at least 2 characters"),
    passengerType: z.enum(["adult", "child", "infant"]),
    dateOfBirth: z.string().refine((date) => {
        const dob = new Date(date);
        const today = new Date();
        return dob < today;
    }, "Date of birth must be in the past"),
    passportInfo: passportInfoSchema,
    email: z.string().email("Please enter a valid email"),
    phone: z
        .string()
        .regex(/^\+?[0-9]\d{1,14}$/, "Please enter a valid phone number"),
    seatAssignments: z
        .array(
            z.object({
                segmentId: z.string(),
                seatNumber: z.string(),
            })
        )
        .optional(),
});

// Schema for the entire form
export const formSchema = z.object({
    passengersDetails: z
        .array(passengerSchema)
        .min(1, "At least one passenger is required"),
});

type FormSchema = z.infer<typeof formSchema>;
type PassengerDetails = z.infer<typeof passengerSchema>;

interface PassengerDetailsProps {
    onValidationChange: (isValid: boolean) => void;
}

export default function PassengerDetails({
    onValidationChange,
}: PassengerDetailsProps) {
    const { bookingForm, setBookingForm } = useBookingStore();
    const [activeTab, setActiveTab] = useState(0);

    const form = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            passengersDetails:
                bookingForm?.passengersDetails?.length > 0
                    ? bookingForm.passengersDetails
                    : [
                          {
                              firstName: "",
                              lastName: "",
                              passengerType: "adult",
                              dateOfBirth: new Date()
                                  .toISOString()
                                  .split("T")[0],
                              passportInfo: {
                                  passportNumber: "",
                                  expiryDate: new Date()
                                      .toISOString()
                                      .split("T")[0],
                                  country: "",
                              },
                              email: "",
                              phone: "",
                              seatAssignments: [],
                          },
                      ],
        },
        mode: "onChange", // Enable real-time validation
    });

    const {
        control,
        formState: { errors },
        watch,
    } = form;

    useEffect(() => {
        const subscription = watch((value) => {
            const validationResult = formSchema.safeParse(value);
            onValidationChange(validationResult.success);
            if (value.passengersDetails) {
                // Always update the form data, even if validation fails
                setBookingForm((prev) => ({
                    ...prev,
                    passengersDetails:
                        value.passengersDetails as PassengerDetails[],
                }));
            }
        });
        return () => subscription.unsubscribe();
    }, [watch, onValidationChange, setBookingForm]);

    const { fields, remove, append } = useFieldArray({
        control,
        name: "passengersDetails",
    });

    // Check if passenger can be removed
    const canRemovePassenger = (index: number) => {
        // Count adults in the current passenger list
        const adultCount = fields.filter(
            (_, i) => watch(`passengersDetails.${i}.passengerType`) === "adult"
        ).length;

        // Check if current passenger is an adult
        const isAdult =
            watch(`passengersDetails.${index}.passengerType`) === "adult";

        // If removing this adult would leave no adults, prevent removal
        if (isAdult && adultCount <= 1) {
            return false;
        }

        return fields.length > 1;
    };

    // Format date for form submission
    const formatDateForSubmission = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    };

    return (
        <Card className="shadow-none rounded-xl max-w-[800px] self-center w-full bg-white">
            <View className="items-center py-4 border-b border-gray-100 px-4">
                <Text className="text-2xl font-bold text-blue-900 mb-1">
                    Passenger Information
                </Text>
                <Text className="text-sm text-gray-500 text-center">
                    Please enter the details for all passengers as they appear
                    on official travel documents
                </Text>
            </View>

            {/* Passenger Tabs */}
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="border-b border-gray-100 mb-4"
                contentContainerStyle={{
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                }}
            >
                {fields.map((field, index) => (
                    <TouchableOpacity
                        key={field.id}
                        className={`flex-row items-center px-3 py-2 rounded-full mr-2 ${
                            activeTab === index ? "bg-blue-500" : "bg-gray-100"
                        }`}
                        onPress={() => setActiveTab(index)}
                    >
                        <User
                            size={16}
                            color={activeTab === index ? "#ffffff" : "#3b82f6"}
                        />
                        <Text
                            className={`ml-1.5 text-sm font-medium ${
                                activeTab === index
                                    ? "text-white"
                                    : "text-blue-500"
                            }`}
                        >
                            {watch(`passengersDetails.${index}.firstName`) ||
                            watch(`passengersDetails.${index}.lastName`)
                                ? `${watch(
                                      `passengersDetails.${index}.firstName`
                                  )} ${watch(
                                      `passengersDetails.${index}.lastName`
                                  )}`.trim()
                                : `Passenger ${index + 1}`}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>

            <ScrollView className="p-4">
                {fields.map((field, index) => (
                    <View
                        key={field.id}
                        className={`mb-6 p-4 rounded-xl bg-white border border-gray-200 ${
                            activeTab !== index ? "hidden" : ""
                        }`}
                    >
                        <View className="flex-row justify-between items-center mb-4">
                            <View className="flex-row items-center">
                                <Text className="text-lg font-semibold text-gray-900 mr-2">
                                    Passenger {index + 1}
                                </Text>
                                <View className="bg-blue-100 px-2 py-0.5 rounded-xl">
                                    <Text className="text-xs font-medium text-blue-500">
                                        {watch(
                                            `passengersDetails.${index}.passengerType`
                                        ) === "adult"
                                            ? "Adult"
                                            : watch(
                                                  `passengersDetails.${index}.passengerType`
                                              ) === "child"
                                            ? "Child"
                                            : "Infant"}
                                    </Text>
                                </View>
                            </View>

                            {canRemovePassenger(index) && (
                                <Button
                                    variant="destructive"
                                    size="small"
                                    onPress={() => {
                                        remove(index);
                                        if (activeTab >= fields.length - 1) {
                                            setActiveTab(
                                                Math.max(0, fields.length - 2)
                                            );
                                        }

                                        // Update booking form to recalculate total amount
                                        setBookingForm((prev) => {
                                            if (
                                                !prev ||
                                                !prev.flightInfo ||
                                                !prev.flightInfo.flights ||
                                                !prev.passengersDetails
                                            ) {
                                                return prev;
                                            }

                                            const updatedPassengers = [
                                                ...prev.passengersDetails,
                                            ];
                                            updatedPassengers.splice(index, 1);

                                            // Get the passenger type that was removed
                                            const removedType =
                                                prev.passengersDetails[index]
                                                    ?.passengerType || "adult";

                                            // Calculate new total based on passenger type and current pricing
                                            const currentTotal =
                                                prev.paymentInfo?.totalAmount ||
                                                0;

                                            let pricePerPassenger = 0;
                                            if (
                                                Array.isArray(
                                                    prev.flightInfo.flights
                                                ) &&
                                                prev.flightInfo.flights.length >
                                                    0
                                            ) {
                                                pricePerPassenger =
                                                    prev.flightInfo.flights.reduce(
                                                        (total, flight) => {
                                                            if (
                                                                !flight ||
                                                                !flight.class ||
                                                                !flight.class
                                                                    .pricing
                                                            ) {
                                                                return total;
                                                            }
                                                            const pricing =
                                                                flight.class
                                                                    .pricing;
                                                            return (
                                                                total +
                                                                (removedType ===
                                                                "adult"
                                                                    ? pricing.adult ||
                                                                      0
                                                                    : removedType ===
                                                                      "child"
                                                                    ? pricing.child ||
                                                                      0
                                                                    : pricing.infant ||
                                                                      0)
                                                            );
                                                        },
                                                        0
                                                    );
                                            }

                                            return {
                                                ...prev,
                                                passengersDetails:
                                                    updatedPassengers,
                                                paymentInfo: {
                                                    ...prev.paymentInfo,
                                                    totalAmount: Math.max(
                                                        0,
                                                        currentTotal -
                                                            pricePerPassenger
                                                    ),
                                                    subTotal: Math.max(
                                                        0,
                                                        (prev.paymentInfo
                                                            ?.subTotal || 0) -
                                                            pricePerPassenger
                                                    ),
                                                },
                                            };
                                        });
                                    }}
                                    className="bg-red-100 px-3"
                                >
                                    <Text className="text-red-500 font-medium">
                                        Remove
                                    </Text>
                                </Button>
                            )}
                        </View>

                        <View className="mb-2 bg-white p-4 ">
                            <Text className="text-base font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                                Personal Information
                            </Text>

                            <View className="flex-row flex-wrap justify-between mb-2">
                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.firstName`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        First Name{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.firstName`}
                                        render={({ field }) => (
                                            <Input
                                                value={field.value}
                                                onChangeText={field.onChange}
                                                placeholder="First Name"
                                                className={`h-12 border rounded-md   ${
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.firstName
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-200"
                                                }`}
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.firstName && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.firstName?.message
                                            }
                                        </Text>
                                    )}
                                </View>

                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.lastName`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Last Name{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.lastName`}
                                        render={({ field }) => (
                                            <Input
                                                value={field.value}
                                                onChangeText={field.onChange}
                                                placeholder="Last Name"
                                                className={`h-12 border rounded-md   ${
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.lastName
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-200"
                                                }`}
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.lastName && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.lastName?.message
                                            }
                                        </Text>
                                    )}
                                </View>
                            </View>

                            <View className="flex-row flex-wrap justify-between mb-2">
                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.passengerType`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Passenger Type{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.passengerType`}
                                        render={({ field }) => (
                                            <View className="w-full">
                                                <Select
                                                    selectedValue={field.value}
                                                    onValueChange={(value) => {
                                                        // Check if changing this would leave no adults
                                                        const adultCount =
                                                            fields.filter(
                                                                (_, i) =>
                                                                    (i === index
                                                                        ? value
                                                                        : watch(
                                                                              `passengersDetails.${i}.passengerType`
                                                                          )) ===
                                                                    "adult"
                                                            ).length;

                                                        // Only allow changing from adult if there would still be at least one adult
                                                        if (
                                                            field.value ===
                                                                "adult" &&
                                                            value !== "adult" &&
                                                            adultCount < 1
                                                        ) {
                                                            // Don't allow the change
                                                            return;
                                                        }

                                                        field.onChange(value);
                                                    }}
                                                    disabled={
                                                        bookingForm?.flightInfo
                                                            ?.flights?.length >
                                                        0
                                                    }
                                                >
                                                    <SelectItem
                                                        label="Adult"
                                                        value="adult"
                                                    />
                                                    <SelectItem
                                                        label="Child"
                                                        value="child"
                                                    />
                                                    <SelectItem
                                                        label="Infant"
                                                        value="infant"
                                                    />
                                                </Select>
                                            </View>
                                        )}
                                    />
                                </View>

                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.dateOfBirth`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Date of Birth{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.dateOfBirth`}
                                        render={({ field }) => (
                                            <DatePicker
                                                date={
                                                    field.value
                                                        ? new Date(field.value)
                                                        : new Date()
                                                }
                                                setDate={(date) => {
                                                    field.onChange(
                                                        formatDateForSubmission(
                                                            date
                                                        )
                                                    );
                                                }}
                                                startYear={1900}
                                                endYear={new Date().getFullYear()}
                                                style={
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.dateOfBirth
                                                        ? {
                                                              borderColor:
                                                                  "#ef4444",
                                                              backgroundColor:
                                                                  "#fef2f2",
                                                          }
                                                        : {}
                                                }
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.dateOfBirth && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.dateOfBirth?.message
                                            }
                                        </Text>
                                    )}
                                </View>
                            </View>
                        </View>

                        <View className="w-full border-b border-b-gray-200" />

                        <View className="mb-2 bg-white  p-4 ">
                            <Text className="text-base font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                                Passport Information
                            </Text>

                            <View className="flex-row flex-wrap justify-between mb-2">
                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.passportInfo.passportNumber`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Passport Number{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <View className="relative">
                                        <View className="absolute left-3 top-4 z-10">
                                            <Passport
                                                size={16}
                                                color="#6b7280"
                                            />
                                        </View>
                                        <Controller
                                            control={control}
                                            name={`passengersDetails.${index}.passportInfo.passportNumber`}
                                            render={({ field }) => (
                                                <Input
                                                    value={field.value}
                                                    onChangeText={
                                                        field.onChange
                                                    }
                                                    placeholder="Passport Number"
                                                    className={`pl-10 h-12 border rounded-md ${
                                                        errors
                                                            .passengersDetails?.[
                                                            index
                                                        ]?.passportInfo
                                                            ?.passportNumber
                                                            ? "border-red-500 bg-red-50"
                                                            : "border-gray-200"
                                                    }`}
                                                />
                                            )}
                                        />
                                    </View>
                                    {errors.passengersDetails?.[index]
                                        ?.passportInfo?.passportNumber && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.passportInfo
                                                    ?.passportNumber?.message
                                            }
                                        </Text>
                                    )}
                                </View>

                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.passportInfo.nationality`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Nationality{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <View className="relative">
                                        <View className="absolute left-3 top-4 z-10">
                                            <Globe size={16} color="#6b7280" />
                                        </View>
                                        <Controller
                                            control={control}
                                            name={`passengersDetails.${index}.passportInfo.country`}
                                            render={({ field }) => (
                                                <Input
                                                    value={field.value}
                                                    onChangeText={
                                                        field.onChange
                                                    }
                                                    placeholder="Nationality"
                                                    className={`pl-10 h-12 border rounded-md ${
                                                        errors
                                                            .passengersDetails?.[
                                                            index
                                                        ]?.passportInfo?.country
                                                            ? "border-red-500 bg-red-50"
                                                            : "border-gray-200"
                                                    }`}
                                                />
                                            )}
                                        />
                                    </View>
                                    {errors.passengersDetails?.[index]
                                        ?.passportInfo?.country && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.passportInfo?.country
                                                    ?.message
                                            }
                                        </Text>
                                    )}
                                </View>
                            </View>

                            <View className="flex-row flex-wrap justify-between mb-2">
                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.passportInfo.expiryDate`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Passport Expiry Date{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.passportInfo.expiryDate`}
                                        render={({ field }) => (
                                            <DatePicker
                                                date={
                                                    field.value
                                                        ? new Date(field.value)
                                                        : new Date()
                                                }
                                                setDate={(date) => {
                                                    field.onChange(
                                                        formatDateForSubmission(
                                                            date
                                                        )
                                                    );
                                                }}
                                                startYear={new Date().getFullYear()}
                                                style={
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.passportInfo?.expiryDate
                                                        ? {
                                                              borderColor:
                                                                  "#ef4444",
                                                              backgroundColor:
                                                                  "#fef2f2",
                                                          }
                                                        : {}
                                                }
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.passportInfo?.expiryDate && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.passportInfo?.expiryDate
                                                    ?.message
                                            }
                                        </Text>
                                    )}
                                </View>
                            </View>
                        </View>

                        <View className="w-full border-b border-b-gray-200" />

                        <View className="bg-white rounded-lg p-4 ">
                            <Text className="text-base font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                                Contact Information
                            </Text>

                            <View className="flex-row flex-wrap justify-between mb-2">
                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.email`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Email{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.email`}
                                        render={({ field }) => (
                                            <Input
                                                value={field.value}
                                                onChangeText={field.onChange}
                                                placeholder="Email"
                                                keyboardType="email-address"
                                                className={`h-12 border rounded-md ${
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.email
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-200"
                                                }`}
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.email && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.email?.message
                                            }
                                        </Text>
                                    )}
                                </View>

                                <View
                                    className={`${
                                        Platform.OS === "web"
                                            ? "w-[48%]"
                                            : "w-full"
                                    } mb-4`}
                                >
                                    <Label
                                        htmlFor={`passengersDetails.${index}.phone`}
                                        className="text-sm font-medium text-gray-900 mb-1.5"
                                    >
                                        Phone{" "}
                                        <Text className="text-red-500">*</Text>
                                    </Label>
                                    <Controller
                                        control={control}
                                        name={`passengersDetails.${index}.phone`}
                                        render={({ field }) => (
                                            <Input
                                                value={field.value}
                                                onChangeText={field.onChange}
                                                placeholder="Phone"
                                                keyboardType="phone-pad"
                                                className={`h-12 border rounded-md ${
                                                    errors.passengersDetails?.[
                                                        index
                                                    ]?.phone
                                                        ? "border-red-500 bg-red-50"
                                                        : "border-gray-200"
                                                }`}
                                            />
                                        )}
                                    />
                                    {errors.passengersDetails?.[index]
                                        ?.phone && (
                                        <Text className="text-xs text-red-500 mt-1">
                                            {
                                                errors.passengersDetails[index]
                                                    ?.phone?.message
                                            }
                                        </Text>
                                    )}
                                </View>
                            </View>
                        </View>
                    </View>
                ))}
            </ScrollView>
        </Card>
    );
}
