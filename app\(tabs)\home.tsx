import {
    View,
    Text,
    ScrollView,
    RefreshControl,
    TouchableOpacity,
    Image,
    useWindowDimensions,
} from "react-native";
import React, { useCallback, useState } from "react";
import { useAuthStore } from "@/stores/auth";
import { SafeAreaView } from "react-native-safe-area-context";
import WalletCard from "@/components/wallet";
import { useQuery } from "@tanstack/react-query";
import apiServices from "@/services/apiServices";
import { WalletResponse } from "@/types/wallet";

import {
    Activity,
    ChevronRight,
    CreditCard,
    DollarSign,
    Users,
} from "@/constants/icons";
import StatsCard from "@/components/stats-card";
import FlightStatusCard from "@/components/FlightStatusCard";
import { UpcomingFlightResponse } from "@/types/flight";
import capitalizeFirstLetter from "@/utils/utilFunctions";
import { StatusBar } from "expo-status-bar";
import { AgentStatsResponse } from "@/types/stats";
import { Skeleton } from "@rneui/themed";

const Home = () => {
    const user = useAuthStore((state) => state.user);
    const { width } = useWindowDimensions();
    const [refreshing, setRefreshing] = useState(false);

    const {
        data: walletInfo,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ["walletInfo", user?._id],
        queryFn: () =>
            apiServices.getWalletInfo(
                user?._id as string
            ) as Promise<WalletResponse>,
        enabled: !!user?._id,
    });

    // Fetch upcoming flights
    const { data: upcomingFlights, isLoading: isUpcomingFlightsLoading } =
        useQuery({
            queryKey: ["upcomingFlights"],
            queryFn: () =>
                apiServices.getUpcomingFlights() as Promise<UpcomingFlightResponse>,
        });

    const { data: agentStats, isLoading: isAgentStatsLoading } = useQuery({
        queryKey: ["agentStats"],
        queryFn: () =>
            apiServices.getAgentStats() as Promise<AgentStatsResponse>,
    });

    const stats = agentStats?.data || {
        activeBookings: 0,
        totalBookings: 0,
        totalCancelled: 0,
        totalPassengers: 0,
        totalRevenue: 0,
    };

    const onRefresh = useCallback(() => {
        setRefreshing(true);
        refetch();
        setTimeout(() => {
            setRefreshing(false);
        }, 2000);
    }, []);
    return (
        <SafeAreaView className="flex-1 bg-gray-50" edges={["top"]}>
            <StatusBar style="inverted" />
            <ScrollView
                className="flex-1"
                contentContainerClassName="pb-10"
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                    />
                }
            >
                {/* Header with profile */}
                <View className="flex-row justify-between items-center px-4 pt-2 pb-4">
                    <View>
                        <Text className="text-sm text-gray-500">
                            Welcome back,
                        </Text>
                        <Text className="text-xl font-bold text-gray-900">
                            {capitalizeFirstLetter(user?.fullName || "")}
                        </Text>
                    </View>
                    <TouchableOpacity>
                        <Image
                            source={{
                                uri: user?.profilePicture,
                            }}
                            className="w-10 h-10 rounded-full"
                            resizeMode="cover"
                        />
                    </TouchableOpacity>
                </View>

                {/* Wallet Card */}
                {isLoading ? (
                    <Skeleton
                        animation="wave"
                        width={width * 0.95}
                        height={150}
                        className="mx-auto"
                    />
                ) : walletInfo?.wallet ? (
                    <WalletCard walletInfo={walletInfo.wallet} />
                ) : (
                    <View className="flex-1 justify-center items-center">
                        <Text className="text-gray-500">
                            No wallet information available
                        </Text>
                    </View>
                )}

                {/* Stats Cards */}
                {isAgentStatsLoading ? (
                    <View className="px-4 mt-4">
                        <Text className="text-xl font-semibold text-[#111827]">
                            Overview
                        </Text>
                        <View className="flex-row justify-between mt-3 flex-wrap ">
                            <Skeleton
                                animation="wave"
                                width={width * 0.45}
                                height={100}
                                className="mx-auto"
                            />
                            <Skeleton
                                animation="wave"
                                width={width * 0.45}
                                height={100}
                                className="mx-auto"
                            />
                        </View>
                    </View>
                ) : (
                    <View className="px-4 mt-4">
                        <Text className="text-xl font-semibold text-[#111827]">
                            Overview
                        </Text>
                        <View className="flex-row justify-between mt-3 flex-wrap ">
                            <StatsCard
                                label="Total Revenue"
                                value={stats.totalRevenue}
                                icon={CreditCard}
                                trend={8.2}
                                isCurrency={true}
                                color="#4f46e5"
                            />
                            <StatsCard
                                label="Total Bookings"
                                value={stats.totalBookings}
                                icon={Activity}
                                trend={15}
                                color="#0891b2"
                            />
                            <StatsCard
                                label="Total Passengers"
                                value={stats.totalPassengers}
                                icon={Users}
                                trend={-4.5}
                                color="#db2777"
                            />
                            <StatsCard
                                label="Active Bookings"
                                value={stats.activeBookings}
                                icon={DollarSign}
                                trend={12.3}
                                color="#16a34a"
                            />
                        </View>
                    </View>
                )}

                {/* Upcoming Flights */}
                {isUpcomingFlightsLoading ? (
                    <View className="mt-4 px-4">
                        <Text className="text-xl font-semibold text-[#111827]">
                            Upcoming Flights
                        </Text>
                        <View className="flex-row justify-between mt-3 flex-wrap ">
                            <Skeleton
                                animation="pulse"
                                width={400}
                                height={200}
                                className="mx-auto "
                                skeletonStyle={{
                                    backgroundColor: "#e5e7eb",
                                }}
                            />
                        </View>
                    </View>
                ) : upcomingFlights?.upcomingFlights &&
                  upcomingFlights.upcomingFlights.length > 0 ? (
                    <View className="mt-4 px-4">
                        <View className="flex-row justify-between items-center mb-4">
                            <Text className="text-xl font-semibold text-[#111827]">
                                Upcoming Flights
                            </Text>
                            <TouchableOpacity className="flex-row items-center">
                                <Text className="text-[#4f46e5] mr-4 text-lg">
                                    See all
                                </Text>
                                <ChevronRight size={16} color="#4f46e5" />
                            </TouchableOpacity>
                        </View>

                        <View>
                            {upcomingFlights?.upcomingFlights.map((item) => (
                                <FlightStatusCard
                                    key={item.flightNumber}
                                    departure={`${capitalizeFirstLetter(
                                        item.route.origin.city
                                    )} (${item.route.origin.airport.toUpperCase()})`}
                                    arrival={`${capitalizeFirstLetter(
                                        item.route.destination.city
                                    )} (${item.route.destination.airport.toUpperCase()})`}
                                    date={item.departure.date.split("T")[0]}
                                    time={item.departure.time}
                                    flightNumber={item.flightNumber}
                                    status="On Time"
                                    airline="American Airlines"
                                />
                            ))}
                        </View>
                    </View>
                ) : (
                    <View className="flex-1 justify-center items-center">
                        <Text className="text-gray-500">
                            No upcoming flights available
                        </Text>
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
};

export default Home;
