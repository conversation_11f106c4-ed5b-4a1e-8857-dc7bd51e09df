import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface ReservationCountdownProps {
  expiryDate: string;
}

export default function ReservationCountdown({ expiryDate }: ReservationCountdownProps) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    isExpired: boolean;
  }>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false,
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const expiryTime = new Date(expiryDate).getTime();
      const now = new Date().getTime();
      const difference = expiryTime - now;

      if (difference <= 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true,
        };
      }

      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
        isExpired: false,
      };
    };

    // Initial calculation
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    // Clean up
    return () => clearInterval(timer);
  }, [expiryDate]);

  // Determine color based on urgency
  const getColorStyle = () => {
    if (timeLeft.isExpired) {
      return styles.expired;
    }
    
    if (timeLeft.days === 0 && timeLeft.hours < 3) {
      return styles.urgent;
    }
    
    if (timeLeft.days === 0 && timeLeft.hours < 12) {
      return styles.warning;
    }
    
    return styles.normal;
  };

  return (
    <View style={styles.container}>
      {timeLeft.isExpired ? (
        <Text style={[styles.expiredText, getColorStyle()]}>EXPIRED</Text>
      ) : (
        <View style={styles.timerContainer}>
          <Text style={styles.timerLabel}>Expires in:</Text>
          <View style={styles.timerUnits}>
            {timeLeft.days > 0 && (
              <View style={styles.timeUnit}>
                <Text style={[styles.timeValue, getColorStyle()]}>{timeLeft.days}</Text>
                <Text style={styles.timeLabel}>days</Text>
              </View>
            )}
            <View style={styles.timeUnit}>
              <Text style={[styles.timeValue, getColorStyle()]}>{timeLeft.hours}</Text>
              <Text style={styles.timeLabel}>hrs</Text>
            </View>
            <View style={styles.timeUnit}>
              <Text style={[styles.timeValue, getColorStyle()]}>{timeLeft.minutes}</Text>
              <Text style={styles.timeLabel}>min</Text>
            </View>
            <View style={styles.timeUnit}>
              <Text style={[styles.timeValue, getColorStyle()]}>{timeLeft.seconds}</Text>
              <Text style={styles.timeLabel}>sec</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  timerUnits: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeUnit: {
    alignItems: 'center',
    marginLeft: 8,
    minWidth: 32,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  timeLabel: {
    fontSize: 10,
    color: '#6b7280',
  },
  normal: {
    color: '#3b82f6',
  },
  warning: {
    color: '#f59e0b',
  },
  urgent: {
    color: '#ef4444',
  },
  expired: {
    color: '#ef4444',
  },
  expiredText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});