import { Tabs } from "@/components/ui/tabs";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { StyleSheet, Text, View } from "react-native";
import { CashPaymentTab } from "./payments/cash-payment-tab";
import { PaymentActions } from "./payments/payment-actions";
import { PaymentStatusAlerts } from "./payments/payment-status-alerts";
import { ReservationSection } from "./payments/reservation-section";
import { WalletPaymentTab } from "./payments/wallet-payment-tab";
import { useBookingStore } from "@/stores/booking-store";

export interface PaymentDetailsFormData {
    paymentMethod: "wallet" | "cash";
    totalAmount: number;
    reservationId?: string;
}

interface PaymentDetailsProps {
    onPaymentValidation: (isValid: boolean) => void;
    onPaymentComplete?: (success: boolean, data?: any) => void;
    isReservationPayment?: boolean;
    initialAmount?: number;
    reservationId?: string;
}

export default function PaymentDetails({
    reservationId,
    onPaymentValidation,
    onPaymentComplete,
    isReservationPayment = false,
    initialAmount = 0,
}: PaymentDetailsProps) {
    const [activeTab, setActiveTab] = useState<string>("wallet");
    const [processingPayment, setProcessingPayment] = useState(false);
    const [processingReservation, setProcessingReservation] = useState(false);
    const [totalAmount, setTotalAmount] = useState(initialAmount);
    const { bookingForm } = useBookingStore();

    const {
        control,
        handleSubmit,
        setValue,
        formState: { isValid },
    } = useForm<PaymentDetailsFormData>({
        defaultValues: {
            reservationId,
            paymentMethod: "wallet",
            totalAmount: initialAmount,
        },
        mode: "onChange",
    });

    useEffect(() => {
        // Update form when initialAmount changes
        setValue("totalAmount", initialAmount);
        setValue("reservationId", reservationId);
        setTotalAmount(initialAmount);
    }, [initialAmount, setValue, reservationId]);

    useEffect(() => {
        onPaymentValidation(isValid);
    }, [isValid, onPaymentValidation]);

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Payment Method</Text>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <Tabs.List>
                    <Tabs.Trigger value="wallet">
                        <Text
                            style={
                                activeTab === "wallet"
                                    ? styles.activeTabText
                                    : styles.tabText
                            }
                        >
                            Wallet
                        </Text>
                    </Tabs.Trigger>
                    <Tabs.Trigger value="cash">
                        <Text
                            style={
                                activeTab === "cash"
                                    ? styles.activeTabText
                                    : styles.tabText
                            }
                        >
                            Cash
                        </Text>
                    </Tabs.Trigger>
                </Tabs.List>

                <View style={styles.tabContent}>
                    <Tabs.Content value="wallet">
                        <WalletPaymentTab totalAmount={totalAmount} />
                    </Tabs.Content>

                    <Tabs.Content value="cash">
                        <CashPaymentTab />
                    </Tabs.Content>
                </View>
            </Tabs>

            <PaymentStatusAlerts />

            {/* Payment Summary */}
            <View style={styles.summaryContainer}>
                <Text style={styles.summaryTitle}>Payment Summary</Text>

                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Total Amount</Text>
                    <Text style={styles.summaryValue}>
                        ${bookingForm?.paymentInfo?.totalAmount.toFixed(2)}
                    </Text>
                </View>

                <Controller
                    control={control}
                    name="totalAmount"
                    render={({ field: { value } }) => (
                        <View style={{ display: "none" }}>
                            <Text>{value}</Text>
                        </View>
                    )}
                />
            </View>

            {/* Reservation Option (only show for new bookings, not for reservation payments) */}
            {!isReservationPayment && (
                <ReservationSection
                    processingReservation={processingReservation}
                    setProcessingReservation={setProcessingReservation}
                    onPaymentComplete={onPaymentComplete}
                />
            )}

            {/* Payment Actions */}
            <PaymentActions
                activeTab={activeTab}
                isValid={isValid}
                processingPayment={processingPayment}
                handleSubmit={handleSubmit}
                setValue={setValue}
                totalAmount={totalAmount}
                setProcessingPayment={setProcessingPayment}
                onPaymentComplete={onPaymentComplete}
                isReservationPayment={isReservationPayment}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        padding: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: "600",
        marginBottom: 16,
        color: "#111827",
    },
    tabContent: {
        marginTop: 16,
    },
    activeTabText: {
        color: "#3b82f6",
        fontWeight: "600",
    },
    tabText: {
        color: "#6b7280",
    },
    summaryContainer: {
        marginTop: 24,
        padding: 16,
        backgroundColor: "#f9fafb",
        borderRadius: 12,
        borderWidth: 1,
        borderColor: "#e5e7eb",
        marginBottom: 16,
    },
    summaryTitle: {
        fontSize: 16,
        fontWeight: "600",
        marginBottom: 12,
        color: "#111827",
    },
    summaryRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        color: "#6b7280",
    },
    summaryValue: {
        fontSize: 14,
        fontWeight: "600",
        color: "#111827",
    },
});
