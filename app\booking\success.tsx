import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Home, Ticket } from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import { Stack, useRouter } from "expo-router";
import React from "react";
import { SafeAreaView, ScrollView, StyleSheet, Text, View } from "react-native";

export default function BookingSuccessScreen() {
    const router = useRouter();
    const { bookingForm } = useBookingStore();

    return (
        <SafeAreaView style={styles.container}>
            <Stack.Screen
                options={{ title: "Booking Confirmed", headerShown: false }}
            />

            <ScrollView contentContainerStyle={styles.content}>
                <View style={styles.successIcon}>
                    <CheckCircle size={80} color="#10b981" />
                </View>

                <Text style={styles.title}>Booking Confirmed!</Text>
                <Text style={styles.subtitle}>
                    Your flight has been successfully booked
                </Text>

                <View style={styles.infoCard}>
                    <View style={styles.infoRow}>
                        <Text style={styles.infoLabel}>Booking Reference:</Text>
                        <Text style={styles.infoValue}>
                            {bookingForm.bookingReference?.bookingNumber ||
                                "N/A"}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={styles.infoLabel}>PNR:</Text>
                        <Text style={styles.infoValue}>
                            {bookingForm.bookingReference?.PNR || "N/A"}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={styles.infoLabel}>Total Amount:</Text>
                        <Text style={styles.infoValue}>
                            ${bookingForm.paymentInfo.totalAmount.toFixed(2)}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={styles.infoLabel}>Payment Status:</Text>
                        <View style={styles.statusBadge}>
                            <Text style={styles.statusText}>Confirmed</Text>
                        </View>
                    </View>
                </View>

                <Text style={styles.noteText}>
                    A confirmation email has been sent to the email address
                    provided during booking.
                </Text>

                <View style={styles.buttonsContainer}>
                    <Button
                        variant="outline"
                        style={styles.viewTicketButton}
                        onPress={() => {}}
                    >
                        <Ticket
                            size={20}
                            color="#3b82f6"
                            style={styles.buttonIcon}
                        />
                        <Text style={styles.viewTicketText}>View E-Ticket</Text>
                    </Button>

                    <Button
                        style={styles.homeButton}
                        onPress={() => router.push("/(tabs)/home")}
                    >
                        <Home
                            size={20}
                            color="#fff"
                            style={styles.buttonIcon}
                        />
                        <Text style={styles.homeButtonText}>Back to Home</Text>
                    </Button>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff",
    },
    content: {
        flexGrow: 1,
        padding: 24,
        alignItems: "center",
        justifyContent: "center",
    },
    successIcon: {
        marginBottom: 24,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#333",
        marginBottom: 8,
        textAlign: "center",
    },
    subtitle: {
        fontSize: 16,
        color: "#666",
        marginBottom: 32,
        textAlign: "center",
    },
    infoCard: {
        width: "100%",
        backgroundColor: "#f8fafc",
        borderRadius: 12,
        padding: 20,
        marginBottom: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    infoRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: "#f0f0f0",
    },
    infoLabel: {
        fontSize: 16,
        color: "#666",
    },
    infoValue: {
        fontSize: 16,
        fontWeight: "600",
        color: "#333",
    },
    statusBadge: {
        backgroundColor: "#dcfce7",
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 16,
    },
    statusText: {
        color: "#10b981",
        fontWeight: "600",
        fontSize: 14,
    },
    noteText: {
        fontSize: 14,
        color: "#666",
        textAlign: "center",
        marginBottom: 32,
    },
    buttonsContainer: {
        width: "100%",
        gap: 16,
    },
    viewTicketButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        borderColor: "#3b82f6",
        borderWidth: 1,
        borderRadius: 8,
        paddingVertical: 12,
    },
    viewTicketText: {
        color: "#3b82f6",
        fontSize: 16,
        fontWeight: "600",
    },
    homeButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#3b82f6",
        borderRadius: 8,
        paddingVertical: 12,
    },
    homeButtonText: {
        color: "#fff",
        fontSize: 16,
        fontWeight: "600",
    },
    buttonIcon: {
        marginRight: 8,
    },
});
