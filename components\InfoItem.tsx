import React from "react";
import { View, Text, Pressable, Platform } from "react-native";
import { Copy } from "@/constants/icons";
import * as Clipboard from "expo-clipboard";

interface InfoItemProps {
    label: string;
    value: string;
    icon?: React.ReactNode;
    copyable?: boolean;
}

export const InfoItem: React.FC<InfoItemProps> = ({
    label,
    value,
    icon,
    copyable = false,
}) => {
    const handleCopy = async () => {
        if (Platform.OS !== "web") {
            await Clipboard.setStringAsync(value);
        } else {
            navigator.clipboard.writeText(value);
        }
    };

    return (
        <View className="flex-row justify-between items-center py-4 border-b border-gray-200">
            <View className="flex-row items-center flex-2">
                {icon && <View className="mr-2">{icon}</View>}
                <Text className="text-sm text-gray-500">{label}</Text>
            </View>
            <View className="flex-row items-center flex-3 justify-end">
                <Text
                    className="text-sm font-medium text-gray-800 text-right"
                    numberOfLines={1}
                >
                    {value}
                </Text>
                {copyable && (
                    <Pressable
                        onPress={handleCopy}
                        className="ml-2 p-1"
                        hitSlop={8}
                    >
                        <Copy size={14} color="#3B82F6" />
                    </Pressable>
                )}
            </View>
        </View>
    );
};
