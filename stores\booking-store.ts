import api from "@/services/api";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

// Types remain the same as before
export interface ClassInfo {
    cabinClass: string;
    availableSeats: number;
    hasEnoughSeats: boolean;
    pricing: {
        adult: number;
        child: number;
        infant: number;
    };
}

export interface FlightSegment {
    _id: string;
    from: string;
    to: string;
    departureDate: string;
    arrivalDate: string;
    departureTime: string;
    arrivalTime: string;
    duration: string;
    status: string;
}

export interface FlightInfo {
    aircraftModel: string;
    flightNumber: string;
    cashPayment: boolean;
    segments: FlightSegment[];
}

export interface Services {
    seatSelection: boolean;
    baggageAllowance: boolean;
    mealService: boolean;
    priorityBoarding: boolean;
}

export interface SelectedFlight {
    flight: FlightInfo;
    class: ClassInfo;
    services: Services;
    seatPrice: number;
    legIndex: number;
}

export interface PassportInfo {
    passportNumber: string;
    expiryDate: string;
    country: string;
}

export interface SeatAssignment {
    segmentId: string;
    seatNumber: string;
}

export interface PassengerDetails {
    firstName: string;
    lastName: string;
    passengerType: "adult" | "child" | "infant";
    dateOfBirth: string;
    class: string;
    passportInfo: PassportInfo;
    email: string;
    phone: string;
    seatAssignments: SeatAssignment[];
}

export interface PaymentInfo {
    totalAmount: number;
    taxAmount: number;
    charges: number;
    subTotal: number;
    paymentMethod?: "wallet" | "cash";
    paymentStatus?: "pending" | "confirmed" | "cancelled";
    paymentCurrency?: string;
    transactionReference?: string;
}

export interface BookingReference {
    bookingId?: string;
    bookingNumber?: string;
    PNR?: string;
    expiresAt?: string;
}

export interface BookingFormData {
    flightInfo: {
        details: {
            numberOfPassengers: number;
            flightType: string;
        };
        flights: SelectedFlight[];
    };
    passengersDetails: PassengerDetails[];
    paymentInfo: PaymentInfo;
    bookingStatus?: "pending" | "booked" | "reserved" | "cancelled";
    bookingReference?: BookingReference;
}

export interface PaginationParams {
    page?: number;
    limit?: number;
    status?: [string];
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    startDate?: string;
    endDate?: string;
}

export interface BookingState {
    bookingForm: BookingFormData;
    setBookingForm: (
        bookingForm:
            | BookingFormData
            | ((prev: BookingFormData) => BookingFormData)
    ) => void;
    createBooking: (data: BookingFormData, userId?: string) => Promise<any>;
    getBookings: (params?: PaginationParams) => Promise<any>;
    getBookingById: (id: string) => Promise<any>;
    getAvailableSeats: (
        segmentId: string,
        departureDate: string
    ) => Promise<any>;
    cancelBooking: (id: string) => Promise<any>;
    confirmBooking: (id: string) => Promise<any>;
    resetBookingForm: () => void;
}

export const defaultBookingForm: BookingFormData = {
    flightInfo: {
        details: {
            numberOfPassengers: 0,
            flightType: "One-Way",
        },
        flights: [],
    },
    passengersDetails: [],
    paymentInfo: {
        totalAmount: 0,
        taxAmount: 0,
        charges: 0,
        subTotal: 0,
    },
    bookingStatus: "pending",
};

// For React Native - using AsyncStorage
export const useBookingStore = create<BookingState>()(
    persist(
        (set, get) => ({
            bookingForm: defaultBookingForm,

            setBookingForm: (bookingForm) => {
                if (typeof bookingForm === "function") {
                    set((state) => ({
                        bookingForm: bookingForm(state.bookingForm),
                    }));
                } else {
                    set({ bookingForm });
                }
            },

            createBooking: async (data, userId) => {
                try {
                    const headers: Record<string, string> = {};
                    if (userId) {
                        headers["x-user-id"] = userId;
                    }

                    const response = await api.post("/bookings", data, {
                        headers,
                    });
                    return response.data;
                } catch (error) {
                    console.error("Error creating booking:", error);
                    throw error;
                }
            },

            getBookings: async (params = {}) => {
                try {
                    const response = await api.get("/bookings/my-booking-all", {
                        params,
                    });
                    return response.data;
                } catch (error) {
                    console.error(error);
                    throw error;
                }
            },

            getBookingById: async (id) => {
                try {
                    const response = await api.get(
                        `/bookings/getBooking/${id}`
                    );
                    return response.data;
                } catch (error) {
                    console.error("Error fetching booking:", error);
                    throw error;
                }
            },

            getAvailableSeats: async (segmentId, departureDate) => {
                try {
                    const response = await api.get(
                        `/setup/flight/available/${segmentId}?departureDate=${departureDate}`
                    );
                    return response.data;
                } catch (error) {
                    console.error("Error fetching available seats:", error);
                    throw error;
                }
            },

            cancelBooking: async (id) => {
                try {
                    const response = await api.patch(`/bookings/cancel/${id}`);
                    return response.data;
                } catch (error) {
                    console.error("Error cancelling booking:", error);
                    throw error;
                }
            },

            confirmBooking: async (id) => {
                try {
                    const response = await api.patch(`/bookings/confirm/${id}`);
                    return response.data;
                } catch (error) {
                    console.error("Error confirming booking:", error);
                    throw error;
                }
            },

            resetBookingForm: () => {
                set({ bookingForm: defaultBookingForm });
            },
        }),
        {
            name: "booking-storage",
            storage: createJSONStorage(() => ({
                getItem: async (name) => {
                    try {
                        const jsonValue = await AsyncStorage.getItem(name);
                        return jsonValue != null ? JSON.parse(jsonValue) : null;
                    } catch (e) {
                        console.error("Error reading from AsyncStorage:", e);
                        return null;
                    }
                },
                setItem: async (name, value) => {
                    try {
                        const jsonValue = JSON.stringify(value);
                        await AsyncStorage.setItem(name, jsonValue);
                    } catch (e) {
                        console.error("Error writing to AsyncStorage:", e);
                    }
                },
                removeItem: async (name) => {
                    try {
                        await AsyncStorage.removeItem(name);
                    } catch (e) {
                        console.error("Error removing from AsyncStorage:", e);
                    }
                },
            })),
        }
    )
);
