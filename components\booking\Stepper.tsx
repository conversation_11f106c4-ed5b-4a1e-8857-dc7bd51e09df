import { Check } from "@/constants/icons";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";

export interface Step {
    name: string;
    icon: React.ComponentType<{
        size?: number;
        color?: string;
        className?: string;
    }>;
}

interface StepperProps {
    current: number;
    steps: Step[];
    onStepClick?: (stepIndex: number) => void;
}

export const Stepper: React.FC<StepperProps> = ({
    current,
    steps,
    onStepClick,
}) => {
    return (
        <View
            className="w-full max-w-lg self-center mt-4"
            accessibilityLabel="Booking progress"
        >
            <View className="flex-row items-center justify-between">
                {steps.map((step, index) => {
                    const StepIcon = step.icon;
                    const isCompleted = index < current;
                    const isCurrent = index === current;
                    const isClickable = isCompleted && onStepClick;

                    return (
                        <View
                            key={step.name}
                            className={`relative flex-1 flex-col items-center ${
                                index !== steps.length - 1 ? "" : ""
                            }`}
                        >
                            {index !== steps.length - 1 && (
                                <View
                                    className={`absolute top-4 left-1/2 h-0.5 w-full ${
                                        isCompleted
                                            ? "bg-blue-600"
                                            : "bg-gray-200"
                                    }`}
                                />
                            )}
                            <View className="relative flex-row items-center justify-center">
                                <TouchableOpacity
                                    className={`w-8 h-8 rounded-full items-center justify-center
                    ${
                        isCompleted
                            ? "bg-blue-600"
                            : isCurrent
                            ? "border-2 border-blue-600 bg-white"
                            : "border-2 border-gray-300 bg-white"
                    }
                    ${isClickable ? "cursor-pointer" : ""}`}
                                    disabled={!isClickable}
                                    onPress={
                                        isClickable
                                            ? () => onStepClick(index)
                                            : undefined
                                    }
                                    accessibilityRole={
                                        isClickable ? "button" : undefined
                                    }
                                    accessibilityState={{ selected: isCurrent }}
                                >
                                    {isCompleted ? (
                                        <Check size={20} color="white" />
                                    ) : (
                                        <StepIcon
                                            size={16}
                                            color={
                                                isCurrent
                                                    ? "#2563eb"
                                                    : "#6b7280"
                                            }
                                        />
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

export default Stepper;
