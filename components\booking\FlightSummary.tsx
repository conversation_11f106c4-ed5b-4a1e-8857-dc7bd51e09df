"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
    Calendar,
    Clock,
    Plane,
    Sunrise,
    Sunset,
    Users,
} from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import {
    capitalizeFirstLetter,
    formatDate,
    formatDuration,
    formatTime,
} from "@/utils/dateFormat";
import { useNavigation } from "@react-navigation/native";
import { Redirect } from "expo-router";
import React, { useEffect } from "react";
import { ScrollView, Text, View } from "react-native";

export default function FlightSummary({ user }: { user?: string }) {
    const { bookingForm } = useBookingStore();
    const navigation = useNavigation();

    useEffect(() => {
        if (bookingForm.flightInfo.flights.length === 0) {
            <Redirect href={"/(tabs)/search"} />;
        }
    }, [bookingForm, navigation, user]);

    return (
        <ScrollView className="flex-1">
            <View className="max-w-[500px] self-center w-full py-4">
                <View className="flex-row items-center justify-between mb-4">
                    <Text className="text-2xl font-bold text-blue-800">
                        Flight Details
                    </Text>
                    {bookingForm && (
                        <View className="flex-row items-center gap-2">
                            <Badge
                                variant="secondary"
                                className="flex-row items-center gap-2 px-4 py-2"
                            >
                                <Users size={16} color="#4b5563" />
                                <Text className="text-sm text-gray-600">
                                    {
                                        bookingForm.flightInfo.details
                                            .numberOfPassengers
                                    }{" "}
                                    Passengers
                                </Text>
                            </Badge>
                            <Badge variant="secondary" className="px-4 py-2">
                                <Text className="text-sm text-gray-600">
                                    {bookingForm.flightInfo.details
                                        .flightType === "Return"
                                        ? "Round Trip"
                                        : bookingForm.flightInfo.details
                                              .flightType}
                                </Text>
                            </Badge>
                        </View>
                    )}
                </View>

                {bookingForm && (
                    <View className="gap-6">
                        {bookingForm.flightInfo.flights.map((flight, index) => (
                            <Card
                                key={index}
                                className="rounded-xl overflow-hidden shadow-md mb-4"
                            >
                                <View className="flex-row justify-between items-center bg-blue-600 px-4 py-3">
                                    <View className="flex-row items-center gap-3">
                                        <Plane size={20} color="white" />
                                        <Text className="text-white font-semibold">
                                            Flight {flight.flight.flightNumber}
                                        </Text>
                                    </View>
                                    <Badge
                                        variant="secondary"
                                        className="bg-blue-500/20 border-0"
                                    >
                                        <Text className="text-white">
                                            {flight.flight.aircraftModel}
                                            {flight.flight.cashPayment ||
                                                " not found"}
                                        </Text>
                                    </Badge>
                                </View>

                                <View className="p-6">
                                    {flight.flight.segments.map(
                                        (segment, segmentIndex) => (
                                            <View
                                                key={segment._id}
                                                className={`${
                                                    segmentIndex !==
                                                    flight.flight.segments
                                                        .length -
                                                        1
                                                        ? "mb-8 pb-8 border-b border-gray-100"
                                                        : ""
                                                }`}
                                            >
                                                <View className="flex-row gap-6">
                                                    {/* Departure */}
                                                    <View className="flex-1">
                                                        <View className="flex-row items-center gap-2 mb-2">
                                                            <Sunrise
                                                                size={16}
                                                                color="#64748b"
                                                            />
                                                            <Text className="text-gray-500 font-medium">
                                                                Departure
                                                            </Text>
                                                        </View>
                                                        <View className="gap-1">
                                                            <Text className="text-lg font-bold">
                                                                {capitalizeFirstLetter(
                                                                    segment.from
                                                                )}
                                                            </Text>
                                                            <Text className="text-2xl font-bold text-blue-600">
                                                                {formatTime(
                                                                    segment.departureDate
                                                                )}
                                                            </Text>
                                                            <View className="flex-row items-center gap-2">
                                                                <Calendar
                                                                    size={16}
                                                                    color="#6b7280"
                                                                />
                                                                <Text className="text-sm text-gray-500">
                                                                    {formatDate(
                                                                        segment.departureDate
                                                                    )}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    {/* Flight Info */}
                                                    <View className="flex-1 items-center justify-center py-4">
                                                        <View className="flex-row items-center gap-3 w-full">
                                                            <View className="h-0.5 flex-1 bg-blue-200" />
                                                            <View className="bg-blue-100 rounded-full p-2">
                                                                <Plane
                                                                    size={24}
                                                                    color="#2563eb"
                                                                    className="rotate-90"
                                                                />
                                                            </View>
                                                            <View className="h-0.5 flex-1 bg-blue-200" />
                                                        </View>
                                                        <View className="items-center mt-4">
                                                            <Text className="text-sm font-medium text-gray-500">
                                                                Flight Duration
                                                            </Text>
                                                            <View className="flex-row items-center gap-2 mt-1">
                                                                <Clock
                                                                    size={16}
                                                                    color="#2563eb"
                                                                />
                                                                <Text className="text-sm font-medium text-blue-600">
                                                                    {formatDuration(
                                                                        Number.parseInt(
                                                                            segment.duration.toString()
                                                                        )
                                                                    )}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    {/* Arrival */}
                                                    <View className="flex-1">
                                                        <View className="flex-row items-center gap-2 mb-2">
                                                            <Sunset
                                                                size={16}
                                                                color="#64748b"
                                                            />
                                                            <Text className="text-gray-500 font-medium">
                                                                Arrival
                                                            </Text>
                                                        </View>
                                                        <View className="gap-1">
                                                            <Text className="text-lg font-bold">
                                                                {capitalizeFirstLetter(
                                                                    segment.to
                                                                )}
                                                            </Text>
                                                            <Text className="text-2xl font-bold text-blue-600">
                                                                {formatTime(
                                                                    segment.arrivalDate
                                                                )}
                                                            </Text>
                                                            <View className="flex-row items-center gap-2">
                                                                <Calendar
                                                                    size={16}
                                                                    color="#6b7280"
                                                                />
                                                                <Text className="text-sm text-gray-500">
                                                                    {formatDate(
                                                                        segment.arrivalDate
                                                                    )}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                </View>
                                            </View>
                                        )
                                    )}
                                </View>
                            </Card>
                        ))}
                    </View>
                )}
            </View>
        </ScrollView>
    );
}
