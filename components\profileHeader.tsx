import React from "react";
import { View, Text, Platform, Pressable, Image } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Plane, MapPin, Shield, ChevronLeft } from "@/constants/icons";
import { useRouter } from "expo-router";
import Animated, { FadeIn } from "react-native-reanimated";
import { ProfileData } from "@/types/profile";

const colors = {
    primary: "#3B82F6", // blue-500
    primaryDark: "#1D4ED8", // blue-700
    card: "#FFFFFF",
    text: "#1F2937", // gray-800
    textSecondary: "#6B7280", // gray-500
    verified: "#10B981", // emerald-500
};

interface ProfileHeaderProps {
    profile: ProfileData;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({ profile }) => {
    const router = useRouter();
    const profileImage =
        profile.documents.personalImage ||
        "https://images.unsplash.com/photo-1507679799987-c73779587ccf?q=80&w=2342&auto=format&fit=crop";

    return (
        <View className="w-full mb-4 fixed">
            <LinearGradient
                colors={[colors.primary, colors.primaryDark]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="pt-12 pb-16 rounded-b-2xl "
                style={
                    Platform.OS === "ios"
                        ? {
                              paddingVertical: 48,
                              borderBottomStartRadius: 50,
                              borderBottomEndRadius: 50,
                          }
                        : {}
                }
            >
                <View className="flex-row items-center justify-between px-4 mb-6">
                    <Pressable
                        className="w-10 h-10 rounded-full bg-white/20 justify-center items-center"
                        onPress={() => router.back()}
                        hitSlop={10}
                    >
                        <ChevronLeft size={24} color={colors.card} />
                    </Pressable>

                    <Text className="text-lg font-semibold text-white">
                        Profile
                    </Text>

                    <View className="w-10" />
                </View>

                <View className="items-center">
                    <Animated.View
                        entering={
                            Platform.OS !== "web"
                                ? FadeIn.delay(200).duration(600)
                                : undefined
                        }
                        className="w-24 h-24  overflow-hidden"
                    >
                        <Image
                            source={{ uri: profileImage }}
                            className="w-full h-full rounded-full border-4 border-white"
                        />
                        {profile.isVerified && (
                            <View className="absolute bottom-0 right-0 z-50 bg-emerald-500 w-6 h-6 rounded-full justify-center items-center border-2 border-white">
                                <Shield size={12} color={colors.card} />
                            </View>
                        )}
                    </Animated.View>
                </View>
            </LinearGradient>

            <View className="bg-white rounded-lg p-4 mx-4 -mt-8 shadow-sm shadow-black/10">
                <View className="mb-3">
                    <Text className="text-xl font-bold text-gray-800 mb-1">
                        {profile.agentName}
                    </Text>
                    <View className="flex-row items-center">
                        <Plane size={14} color={colors.primary} />
                        <Text className="text-sm text-gray-500 ml-1 capitalize">
                            {profile.accountType}
                        </Text>
                    </View>
                </View>

                <View className="flex-row items-center justify-between">
                    {profile.isSuperAgent && (
                        <View className="bg-blue-500/10 py-1 px-2 rounded-full">
                            <Text className="text-xs font-medium text-blue-500">
                                Super Agent
                            </Text>
                        </View>
                    )}

                    <View className="flex-row items-center">
                        <MapPin size={14} color={colors.textSecondary} />
                        <Text className="text-sm text-gray-500 ml-1 capitalize">
                            {profile.assignedCityName}
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );
};
