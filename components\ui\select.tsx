import { Check, ChevronDown } from "@/constants/icons"
import React, { useMemo } from "react"
import { FlatList, Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native"

export interface SelectItem {
label: string
value: string
}

interface SelectProps {
selectedValue: string
onValueChange: (value: string) => void
placeholder?: string
items?: SelectItem[]
disabled?: boolean
style?: any
children?: React.ReactNode
}

export function Select({
selectedValue,
onValueChange,
placeholder = "Select an option",
items: propItems = [],
disabled = false,
style,
children,
}: SelectProps) {
const [visible, setVisible] = React.useState(false)

// Extract items from children or use provided items
const items = useMemo(() => {
  if (children && React.Children.count(children) > 0) {
    const childItems: SelectItem[] = []
    React.Children.forEach(children, (child) => {
      if (React.isValidElement(child) && child.props.label && child.props.value) {
        childItems.push({
          label: child.props.label,
          value: child.props.value,
        })
      }
    })
    return childItems.length > 0 ? childItems : propItems
  }
  return propItems
}, [children, propItems])

// Find selected item
const selectedItem = useMemo(() => {
  return items.find(item => item.value === selectedValue)
}, [items, selectedValue])

return (
  <View style={[styles.container, style]}>
    <TouchableOpacity
      style={[styles.trigger, disabled && styles.disabled]}
      onPress={() => !disabled && setVisible(true)}
      activeOpacity={disabled ? 1 : 0.7}
    >
      <Text style={[styles.triggerText, !selectedItem && styles.placeholder]}>
        {selectedItem ? selectedItem.label : placeholder}
      </Text>
      <ChevronDown size={20} color={disabled ? "#a1a1aa" : "#71717a"} />
    </TouchableOpacity>

    <Modal visible={visible} transparent animationType="fade">
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setVisible(false)}
      >
        <View style={styles.modalContent}>
          <FlatList
            data={items}
            keyExtractor={(item, index) => `${item.value}-${index}`}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.item}
                onPress={() => {
                  onValueChange(item.value)
                  setVisible(false)
                }}
              >
                <Text style={styles.itemText}>{item.label}</Text>
                {item.value === selectedValue && (
                  <Check size={16} color="#2563eb" />
                )}
              </TouchableOpacity>
            )}
            ListEmptyComponent={
              <Text style={styles.noItems}>No options available</Text>
            }
          />
        </View>
      </TouchableOpacity>
    </Modal>
  </View>
)
}

export function SelectItem({ label, value }: SelectItem) {
// This is just a type definition for use with the Select component
return null
}

const styles = StyleSheet.create({
container: {
  position: "relative",
  width: "100%",
},
trigger: {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  borderWidth: 1,
  borderColor: "#e2e8f0",
  borderRadius: 6,
  paddingHorizontal: 12,
  paddingVertical: 8,
  backgroundColor: "white",
},
disabled: {
  opacity: 0.5,
},
triggerText: {
  fontSize: 14,
  color: "#0f172a",
},
placeholder: {
  color: "#94a3b8",
},
modalOverlay: {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
},
modalContent: {
  width: "80%",
  maxHeight: "70%",
  backgroundColor: "white",
  borderRadius: 8,
  padding: 8,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
},
item: {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: 12,
  paddingHorizontal: 16,
  borderRadius: 4,
},
itemText: {
  fontSize: 14,
  color: "#0f172a",
},
noItems: {
  padding: 16,
  textAlign: "center",
  color: "#94a3b8",
},
})