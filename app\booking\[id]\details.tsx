import ReservationCountdown from "@/components/booking/reservationexpireDate";
import TicketDownload from "@/components/booking/TicketDownload";
import { Button } from "@/components/ui/button";
import {
    ArrowLeft,
    Calendar,
    ChevronLeft,
    Clock,
    Plane,
    User,
} from "@/constants/icons";
import api from "@/services/api";
import { useBookingStore } from "@/stores/booking-store";
import { capitalizeFirstLetter, formatDuration } from "@/utils/dateFormat";
import { useQuery } from "@tanstack/react-query";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Share,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function BookingDetails() {
    const { id } = useLocalSearchParams();
    const router = useRouter();
    const { getBookingById } = useBookingStore();
    const [isCancelling, setIsCancelling] = useState(false);

    const capitalizeName = (name: string) => {
        return name
            .split(" ")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
    };

    // Fetch booking details using React Query
    const {
        data: response,
        isLoading,
        isError,
        error,
        refetch,
    } = useQuery({
        queryKey: ["booking", id],
        queryFn: () => getBookingById(id as string),
        enabled: !!id,
    });

    const booking = response?.data;

    const handleCancel = async () => {
        if (!booking) return;

        try {
            setIsCancelling(true);
            const response = await api.patch("/bookings/cancel/" + booking._id);
            if (response.status === 200) {
                Alert.alert("Success", "Booking cancelled successfully");
                router.push("/bookings");
            } else {
                Alert.alert("Error", "Failed to cancel booking");
            }
        } catch (error) {
            console.error("Error cancelling booking:", error);
            Alert.alert("Error", "Failed to cancel booking");
        } finally {
            setIsCancelling(false);
        }
    };

    const handleShare = async () => {
        if (!booking) return;

        try {
            const message = `
Booking Details:
Booking Number: ${booking.bookingNumber}
PNR: ${booking.PNR}
Status: ${booking.status}

Flight Details:
${booking.flightSegments
    .map(
        (segment: any, index: any) => `
Flight ${index + 1}: ${segment.flight.flightNumber}
From: ${segment.from}
To: ${segment.to}
Departure: ${formatDate(segment.departureDate)} ${
            segment.departureTime
                ? `at ${formatTime(segment.departureTime)}`
                : ""
        }
${
    segment.arrivalDate
        ? `Arrival: ${formatDate(segment.arrivalDate)} ${
              segment.arrivalTime ? `at ${formatTime(segment.arrivalTime)}` : ""
          }`
        : ""
}
`
    )
    .join("\n")}

Passengers: ${booking.passengers.length}
${
    booking.payment.totalAmount
        ? `Total Amount: $${booking.payment.totalAmount.toFixed(2)}`
        : ""
}
    `;

            await Share.share({
                message,
                title: `Booking ${booking.bookingNumber}`,
            });
        } catch (error) {
            console.error("Error sharing booking:", error);
            Alert.alert("Error", "Failed to share booking details");
        }
    };

    if (isLoading) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <ActivityIndicator
                    className="flex-1 justify-center items-center"
                    size="large"
                    color="#4f46e5"
                />
            </SafeAreaView>
        );
    }

    if (isError || !booking) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-100 bg-white">
                    <TouchableOpacity
                        className="p-2 rounded-lg bg-gray-100"
                        onPress={() => router.back()}
                    >
                        <ArrowLeft size={20} color="#111827" />
                    </TouchableOpacity>
                    <Text className="text-lg font-semibold text-gray-900">
                        Booking Details
                    </Text>
                    <View className="w-10" />
                </View>

                <View className="flex-1 justify-center items-center p-4">
                    <Text className="text-xl font-bold text-red-500 mb-2">
                        Error
                    </Text>
                    <Text className="text-base text-gray-500 text-center mb-6">
                        {error instanceof Error
                            ? error.message
                            : "Failed to fetch booking details"}
                    </Text>
                    <TouchableOpacity
                        className="bg-indigo-600 px-6 py-3 rounded-lg"
                        onPress={() => refetch()}
                    >
                        <Text className="text-white font-medium">Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-gray-50" edges={["top"]}>
            {/* Header */}
            <View className="flex-row items-center px-4 py-3 border-b border-gray-200">
                <TouchableOpacity
                    onPress={() => router.back()}
                    className="mr-4"
                >
                    <ChevronLeft size={24} color="#3b82f6" />
                </TouchableOpacity>
                <Text className="text-lg font-semibold text-black">
                    Booking Details
                </Text>
            </View>

            <ScrollView
                className="flex-1 p-4"
                showsVerticalScrollIndicator={false}
            >
                {/* Booking Summary Card */}
                <View className="bg-white rounded-xl p-4 mb-4 shadow-sm shadow-blue-50">
                    <View className="flex-row justify-between items-center">
                        <View>
                            <Text className="text-base font-bold text-gray-900">
                                Booking #{booking.bookingNumber}
                            </Text>
                            {booking.PNR && (
                                <Text className="text-sm text-gray-500 mt-0.5">
                                    PNR: {booking.PNR}
                                </Text>
                            )}
                        </View>
                        <View
                            className="px-2 py-1 rounded-full"
                            style={{
                                backgroundColor: `${getStatusColor(
                                    booking.status
                                )}20`,
                            }}
                        >
                            <Text
                                className="text-xs font-medium capitalize"
                                style={{
                                    color: getStatusColor(booking.status),
                                }}
                            >
                                {booking.status}
                            </Text>
                        </View>
                    </View>

                    <View className="h-px bg-gray-100 my-3" />

                    <View className="flex-row flex-wrap">
                        <View className="w-1/2 mb-3">
                            <Text className="text-xs text-gray-500 mb-0.5">
                                Trip Type
                            </Text>
                            <Text className="text-sm font-semibold text-gray-900">
                                {booking.tripType === "ROUND_TRIP"
                                    ? "Round Trip"
                                    : booking.tripType === "ONE_WAY" ||
                                      booking.tripType === "One-Way"
                                    ? "One Way"
                                    : booking.tripType === "MULTI_CITY"
                                    ? "Multi-City"
                                    : booking.tripType}
                            </Text>
                        </View>

                        <View className="w-1/2 mb-3">
                            <Text className="text-xs text-gray-500 mb-0.5">
                                Passengers
                            </Text>
                            <Text className="text-sm font-semibold text-gray-900">
                                {booking.passengers.length}
                            </Text>
                        </View>

                        <View className="w-1/2 mb-3">
                            <Text className="text-xs text-gray-500 mb-0.5">
                                Payment Status
                            </Text>
                            <Text
                                className="text-sm font-semibold"
                                style={{
                                    color: getStatusColor(
                                        booking.payment.paymentStatus
                                    ),
                                }}
                            >
                                {capitalizeFirstLetter(
                                    booking.payment.paymentStatus
                                )}
                            </Text>
                        </View>

                        {booking.payment.totalAmount && (
                            <View className="w-1/2 mb-3">
                                <Text className="text-xs text-gray-500 mb-0.5">
                                    Total Amount
                                </Text>
                                <Text className="text-sm font-semibold text-gray-900">
                                    ${booking.payment.totalAmount.toFixed(2)}
                                </Text>
                            </View>
                        )}

                        <View className="w-1/2">
                            <Text className="text-xs text-gray-500 mb-0.5">
                                Booked By
                            </Text>
                            <Text className="text-sm font-semibold text-gray-900">
                                {booking.bookedBy?.fullName || "N/A"}
                            </Text>
                        </View>

                        <View className="w-1/2">
                            <Text className="text-xs text-gray-500 mb-0.5">
                                Booking Date
                            </Text>
                            <Text className="text-sm font-semibold text-gray-900">
                                {formatDate(booking.createdAt)}
                            </Text>
                        </View>
                    </View>

                    {/* Reservation Expiry Warning */}
                    {booking.status.toLowerCase() === "reserved" &&
                        booking.reservationExpiry && (
                            <View className="mt-3 pt-3 border-t border-gray-100">
                                <View className="bg-amber-50 p-3 rounded-lg">
                                    <Text className="text-amber-800 font-medium">
                                        Reservation Expires Soon
                                    </Text>

                                    <ReservationCountdown
                                        expiryDate={booking.reservationExpiry}
                                    />

                                    <Text className="text-amber-700 text-sm mt-2">
                                        Your reservation will be automatically
                                        cancelled if payment is not completed
                                        before expiry.
                                    </Text>

                                    <Button
                                        className="mt-3 bg-amber-600"
                                        onPress={() =>
                                            router.push(
                                                `/booking/${id}/confirm`
                                            )
                                        }
                                    >
                                        <Text className="text-white font-medium">
                                            Complete Payment
                                        </Text>
                                    </Button>
                                </View>
                            </View>
                        )}
                </View>

                {/* Ticket Download */}
                {booking.status.toLowerCase() !== "reserved" &&
                    booking.payment.paymentStatus.toLowerCase() ===
                        "confirmed" && <TicketDownload booking={booking} />}

                {booking.status.toLowerCase() !== "cancelled" && (
                    <View className="mb-4">
                        <Button className="bg-red-600" onPress={handleCancel}>
                            {isCancelling ? (
                                <ActivityIndicator size="small" color="#fff" />
                            ) : (
                                "Cancel Booking"
                            )}
                        </Button>
                    </View>
                )}

                {/* Flight Segments */}
                <Text className="text-lg font-bold text-gray-900 mb-3 mt-2">
                    Flight Details
                </Text>

                {booking.flightSegments.map((segment: any, index: any) => (
                    <View
                        key={segment._id}
                        className="bg-white rounded-xl p-4 mb-4 shadow-sm shadow-blue-50"
                    >
                        <View className="flex-row justify-between items-center mb-3">
                            <View className="flex-row items-center">
                                <Plane size={16} color="#4f46e5" />
                                <Text className="text-base font-semibold text-gray-900 ml-1.5">
                                    Flight {segment.flight.flightNumber}
                                </Text>
                            </View>
                            <Text className="text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-md">
                                {index === 0 ? "Outbound" : "Return"}
                            </Text>
                        </View>

                        <View className="flex-row items-center justify-between mb-4">
                            <View className="items-center">
                                <Text className="text-lg font-bold text-gray-900 mb-1">
                                    {segment.from?.split(", ")?.[1] ||
                                        capitalizeFirstLetter(segment.from)}
                                </Text>
                                <Text className="text-sm text-gray-500 text-center max-w-[100px]">
                                    {capitalizeFirstLetter(
                                        segment.from?.split(", ")?.[0]
                                    ) || ""}
                                </Text>
                            </View>

                            <View className="flex-1 items-center relative px-2">
                                <View className="h-[1px] bg-gray-200 w-full absolute top-3 bottom-4 translate-y-1/2" />
                                <View className="bg-white p-1 rounded-xl border  border-gray-200 rotate-45">
                                    <Plane size={16} color="#4f46e5" />
                                </View>
                                <Text className="text-xs text-gray-500 mt-1">
                                    {formatDuration(segment.duration) || "N/A"}
                                </Text>
                            </View>

                            <View className="items-center">
                                <Text className="text-lg font-bold text-gray-900 mb-1">
                                    {segment.to?.split(", ")?.[1] ||
                                        capitalizeFirstLetter(segment.to)}
                                </Text>
                                <Text className="text-sm text-gray-500 text-center max-w-[100px]">
                                    {capitalizeFirstLetter(
                                        segment.to?.split(", ")?.[0]
                                    ) || ""}
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row justify-between mb-3">
                            <View className="flex-1">
                                <View className="flex-row items-center mb-1">
                                    <Calendar size={16} color="#6b7280" />
                                    <Text className="text-sm text-gray-500 ml-1.5">
                                        {formatDate(segment.departureDate)}
                                    </Text>
                                </View>
                                <View className="flex-row items-center">
                                    <Clock size={16} color="#6b7280" />
                                    <Text className="text-sm text-gray-500 ml-1.5">
                                        {formatTime(
                                            segment.departureTime || ""
                                        )}
                                    </Text>
                                </View>
                            </View>

                            {segment.arrivalDate && (
                                <View className="flex-1">
                                    <View className="flex-row items-center mb-1">
                                        <Calendar size={16} color="#6b7280" />
                                        <Text className="text-sm text-gray-500 ml-1.5">
                                            {formatDate(segment.arrivalDate)}
                                        </Text>
                                    </View>
                                    <View className="flex-row items-center">
                                        <Clock size={16} color="#6b7280" />
                                        <Text className="text-sm text-gray-500 ml-1.5">
                                            {formatTime(
                                                segment.arrivalTime || ""
                                            )}
                                        </Text>
                                    </View>
                                </View>
                            )}
                        </View>

                        <View className="flex-row items-center mt-2 pt-2 border-t border-gray-100">
                            <Text className="text-sm text-gray-500 mr-1">
                                Status:
                            </Text>
                            <Text
                                className="text-sm font-semibold"
                                style={{
                                    color: getStatusColor(segment.status),
                                }}
                            >
                                {segment.status}
                            </Text>
                        </View>
                    </View>
                ))}

                {/* Passenger Details */}
                <Text className="text-lg font-bold text-gray-900 mb-3 mt-2">
                    Passenger Information
                </Text>

                {booking.passengers.map((passenger: any) => (
                    <View
                        key={passenger._id}
                        className="bg-white rounded-xl p-4 mb-4 shadow-sm shadow-blue-50"
                    >
                        <View className="flex-row justify-between items-center mb-3">
                            <View className="flex-row items-center">
                                <User size={16} color="#4f46e5" />
                                <Text className="text-base font-semibold text-gray-900 ml-1.5">
                                    {capitalizeName(passenger.firstName)}{" "}
                                    {capitalizeName(passenger.lastName)}
                                </Text>
                            </View>
                            <View className="bg-gray-100 px-2 py-0.5 rounded-md">
                                <Text className="text-xs text-gray-500 capitalize">
                                    {passenger.passengerType}
                                </Text>
                            </View>
                        </View>

                        <View className="mb-3">
                            {passenger.dateOfBirth && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Date of Birth
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {formatDate(passenger.dateOfBirth)}
                                    </Text>
                                </View>
                            )}

                            {passenger.passportInfo?.passportNumber && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Passport Number
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {passenger.passportInfo.passportNumber}
                                    </Text>
                                </View>
                            )}

                            {passenger.passportInfo?.expiryDate && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Passport Expiry
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {formatDate(
                                            passenger.passportInfo.expiryDate
                                        )}
                                    </Text>
                                </View>
                            )}

                            {passenger.passportInfo?.country && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Nationality
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {passenger.passportInfo.country}
                                    </Text>
                                </View>
                            )}

                            {passenger.email && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Email
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {passenger.email}
                                    </Text>
                                </View>
                            )}

                            {passenger.phone && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Phone
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {passenger.phone}
                                    </Text>
                                </View>
                            )}
                        </View>

                        {passenger.seatAssignments &&
                            passenger.seatAssignments.length > 0 && (
                                <View className="mt-2 pt-2 border-t border-gray-100">
                                    <Text className="text-sm font-semibold text-gray-900 mb-2">
                                        Seat Assignments
                                    </Text>
                                    {passenger.seatAssignments.map(
                                        (seat: any, seatIndex: any) => (
                                            <View
                                                key={seatIndex}
                                                className="flex-row mb-1"
                                            >
                                                <Text className="text-sm text-gray-500 mr-2">
                                                    {booking.flightSegments.find(
                                                        (s: any) =>
                                                            s._id ===
                                                            seat.segmentId
                                                    )?.flight.flightNumber ||
                                                        "Flight"}
                                                    :
                                                </Text>
                                                <Text className="text-sm font-medium text-gray-900">
                                                    {seat.seatNumber}
                                                </Text>
                                            </View>
                                        )
                                    )}
                                </View>
                            )}
                    </View>
                ))}

                {/* Payment Details */}
                {(booking.payment.totalAmount ||
                    booking.payment.subTotal ||
                    booking.payment.taxAmount) && (
                    <>
                        <Text className="text-lg font-bold text-gray-900 mb-3 mt-2">
                            Payment Details
                        </Text>

                        <View className="bg-white rounded-xl p-4 mb-4 shadow-sm shadow-blue-50">
                            {booking.payment.subTotal && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Subtotal
                                    </Text>
                                    <Text className="text-sm text-gray-900">
                                        ${booking.payment.subTotal.toFixed(2)}
                                    </Text>
                                </View>
                            )}

                            {booking.payment.taxAmount && (
                                <View className="flex-row justify-between mb-2">
                                    <Text className="text-sm text-gray-500">
                                        Taxes
                                    </Text>
                                    <Text className="text-sm text-gray-900">
                                        ${booking.payment.taxAmount.toFixed(2)}
                                    </Text>
                                </View>
                            )}

                            {booking.payment.charges &&
                                booking.payment.charges > 0 && (
                                    <View className="flex-row justify-between mb-2 ">
                                        <Text className="text-sm text-gray-500 line-through">
                                            Commissions
                                        </Text>
                                        <Text className="text-sm text-gray-900">
                                            $
                                            {booking.payment.charges.toFixed(2)}
                                        </Text>
                                    </View>
                                )}

                            <View className="h-px bg-gray-100 my-3" />

                            <View className="flex-row justify-between">
                                <Text className="text-base font-semibold text-gray-900">
                                    Total
                                </Text>
                                <Text className="text-base font-bold text-indigo-600">
                                    $
                                    {Number(booking.payment.totalAmount) +
                                        Number(booking.payment.taxAmount) ||
                                        "0.00"}
                                </Text>
                            </View>
                        </View>
                    </>
                )}

                {/* Bottom padding */}
                <View className="h-10" />
            </ScrollView>
        </SafeAreaView>
    );
}

// Helper function to get status color
const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
        case "confirmed":
            return "#16a34a";
        case "pending":
            return "#eab308";
        case "cancelled":
            return "#dc2626";
        case "completed":
            return "#6b7280";
        case "booked":
            return "#16a34a";
        case "reserved":
            return "#3b82f6";
        default:
            return "#6b7280";
    }
};

// Helper functions for formatting
const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(
        undefined,
        options as Intl.DateTimeFormatOptions
    );
};

const formatTime = (timeString: string) => {
    if (!timeString) return "";
    return timeString;
};
