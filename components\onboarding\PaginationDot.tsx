import React from "react";
import { Animated } from "react-native";
import { View } from "react-native";

interface PaginationDotProps {
    index: number;
    currentIndex: number;
    length: number;
}

export const PaginationDot: React.FC<PaginationDotProps> = ({
    index,
    currentIndex,
}) => {
    const opacity = currentIndex === index ? 1 : 0.4;
    const width = currentIndex === index ? 24 : 8;
    const backgroundColor = currentIndex === index ? "#FFD700" : "#003087"; // secondary : primary

    return (
        <View className="h-2 mx-1">
            <Animated.View
                style={{
                    width,
                    opacity,
                    backgroundColor,
                }}
                className="h-2 rounded-full"
            />
        </View>
    );
};
