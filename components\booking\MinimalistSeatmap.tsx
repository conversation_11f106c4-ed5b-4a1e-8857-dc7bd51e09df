import { Card } from "@/components/ui/card"
import { Plane } from "@/constants/icons"
import React from "react"
import { ScrollView, Text, TouchableOpacity, View } from "react-native"

interface SeatConfig {
  class: string
  numberOfSeats: number
  layout: string
  features: string[]
}

export interface Seat {
  seatNumber: string
  class: string
  status: string
}

export interface SeatmapProps {
  aircraft: {
    model: string
    registrationName: string
    totalSeats: number
    seatConfig: SeatConfig[]
  }
  seats: Seat[]
  canSelect: boolean
  seatPrice: number
}

// Extend props to include controlled selection options.
interface MinimalistSeatmapProps extends SeatmapProps {
  selectedSeat?: string | null
  onSeatSelect?: (seatNumber: string) => void
  currentPassengerClass?: string
}

const MinimalistSeatmap: React.FC<MinimalistSeatmapProps> = ({
  seatPrice,
  canSelect,
  aircraft,
  seats,
  selectedSeat,
  onSeatSelect,
  currentPassengerClass,
}) => {
  const renderSeat = (seat: Seat | null) => {
    if (!seat) {
      return <View className="w-8 h-10" />
    }

    const isAvailable = seat.status === "Available"
    const isBusiness = seat.class === "Business"
    const isFirst = seat.class === "First"
    const isSelected = selectedSeat === seat.seatNumber

    // Check if this seat is in a different class than the passenger's class
    // This will be used to visually indicate seats that cannot be selected
    const isDifferentClass = currentPassengerClass && seat.class.toLowerCase() !== currentPassengerClass.toLowerCase()

    return (
      <TouchableOpacity
        key={seat.seatNumber}
        disabled={!isAvailable || !canSelect || isDifferentClass}
        onPress={() => isAvailable && onSeatSelect && onSeatSelect(seat.seatNumber)}
        className={`relative w-8 h-10 items-center ${
          !isAvailable || !canSelect || isDifferentClass ? "opacity-50" : ""
        }`}
      >
        {/* Seat back */}
        <View
          className={`absolute top-0 left-1/2 -translate-x-3 w-6 h-4 rounded-t-lg shadow-sm
            ${
              isSelected
                ? "bg-blue-600 border-2 border-blue-300"
                : isBusiness
                  ? "bg-blue-500"
                  : isFirst
                    ? "bg-amber-500"
                    : "bg-gray-500"
            }
            ${isDifferentClass ? "border border-red-300" : ""}`}
        />

        {/* Seat bottom */}
        <View
          className={`absolute bottom-0 left-1/2 -translate-x-3.5 w-7 h-5 rounded shadow-sm
            ${
              isSelected
                ? "bg-blue-600 border-2 border-blue-300"
                : isBusiness
                  ? "bg-blue-500"
                  : isFirst
                    ? "bg-amber-500"
                    : "bg-gray-500"
            }
          ${isDifferentClass ? "border border-red-300" : ""}`}
        >
          {/* Seat cushion detail */}
          <View
            className={`absolute top-1 left-1/2 -translate-x-2.5 w-5 h-3 rounded
            ${isSelected ? "bg-white/20" : "bg-white/10"}`}
          />
        </View>

        {/* Seat number */}
        <Text
          className={`absolute bottom-[-20px] left-1/2 -translate-x-3 text-xs font-medium
          ${isSelected ? "text-blue-800 font-bold" : "text-gray-600"}`}
        >
          {seat.seatNumber}
        </Text>

        {/* Selection indicator */}
        {isSelected && (
          <View className="absolute -top-2 -right-2 w-4 h-4 bg-blue-600 rounded-full items-center justify-center">
            <View className="w-2 h-2 bg-white rounded-full" />
          </View>
        )}
      </TouchableOpacity>
    )
  }

  const renderRow = (rowSeats: (Seat | null)[], layout: string) => {
    const seatGroups = layout.split("-").map(Number)
    let currentIndex = 0

    return (
      <View className="flex-row items-center justify-center">
        {seatGroups.map((groupSize, groupIndex) => {
          const seatGroup = rowSeats.slice(currentIndex, currentIndex + groupSize)
          currentIndex += groupSize

          return (
            <React.Fragment key={groupIndex}>
              <View className="flex-row gap-1">
                {seatGroup.map((seat, seatIndex) => (
                  <React.Fragment key={seat?.seatNumber || `empty-${groupIndex}-${seatIndex}`}>
                    {renderSeat(seat)}
                  </React.Fragment>
                ))}
              </View>
              {groupIndex < seatGroups.length - 1 && <View className="w-8" />}
            </React.Fragment>
          )
        })}
      </View>
    )
  }

  // Group seats by class and row
  const seatsByClass = aircraft.seatConfig.map((config) => {
    const classSeats = seats.filter((seat) => seat.class === config.class)
    const rows: (Seat | null)[][] = []
    let currentRow: (Seat | null)[] = []
    const seatsPerRow = config.layout.split("-").reduce((a, b) => a + Number(b), 0)

    for (let i = 0; i < config.numberOfSeats; i++) {
      if (i < classSeats.length) {
        currentRow.push(classSeats[i])
      } else {
        currentRow.push(null)
      }
      if (currentRow.length === seatsPerRow) {
        rows.push(currentRow)
        currentRow = []
      }
    }
    if (currentRow.length > 0) {
      while (currentRow.length < seatsPerRow) {
        currentRow.push(null)
      }
      rows.push(currentRow)
    }

    return {
      config,
      rows,
    }
  })

  return (
    <Card className="w-full max-w-[500px] self-center overflow-hidden border border-gray-200 shadow-md">
      <View className="bg-blue-600 p-4">
        <View className="flex-row items-center gap-2">
          <Plane size={20} color="white" />
          <Text className="text-white text-lg font-bold">{aircraft.model} Seating Chart</Text>
        </View>
        <Text className="text-blue-200 text-xs mt-1">
          {aircraft.registrationName} • {aircraft.totalSeats} Total Seats
        </Text>
      </View>
      <ScrollView className="p-0">
        <View className="relative p-8 bg-gray-50">
          {/* Aircraft nose */}
          <View className="absolute top-[-24px] left-1/2 -translate-x-12 w-24 h-6 bg-blue-500 rounded-t-3xl" />

          <View className="mt-4 gap-8">
            {seatsByClass.map(({ config, rows }) => (
              <View key={config.class} className="gap-5">
                <View className="bg-gray-200 py-1 px-2 rounded-full self-center mb-4">
                  <Text className="text-xs font-medium text-center">{config.class} Class</Text>
                </View>
                {rows.map((row, rowIndex) => (
                  <View key={rowIndex} className="flex-row items-center justify-center">
                    <Text className="w-6 text-xs text-right pr-2 text-gray-500 font-mono">{rowIndex + 1}</Text>
                    {renderRow(row, config.layout)}
                  </View>
                ))}
              </View>
            ))}
          </View>
        </View>

        {/* Legend */}
        <View className="bg-gray-100 rounded-lg p-3 flex-row justify-between items-center mx-4 my-4">
          <View className="flex-row items-center gap-2">
            <View className="w-4 h-4 rounded bg-amber-500" />
            <Text className="text-xs">First</Text>
          </View>
          <View className="flex-row items-center gap-2">
            <View className="w-4 h-4 rounded bg-blue-500" />
            <Text className="text-xs">Business</Text>
          </View>
          <View className="flex-row items-center gap-2">
            <View className="w-4 h-4 rounded bg-gray-500" />
            <Text className="text-xs">Economy</Text>
          </View>
          {selectedSeat && (
            <View className="flex-row items-center gap-2">
              <View className="w-4 h-4 bg-blue-600 rounded-full relative">
                <View className="absolute top-1 left-1 w-2 h-2 bg-white rounded-full" />
              </View>
              <Text className="text-xs">Selected</Text>
            </View>
          )}
        </View>

        {canSelect && (
          <View>
            <Text className="text-center text-sm text-gray-500 mb-4">
              Click on an available seat to select it
              {seatPrice > 0 && <Text className="text-blue-600 font-medium"> (${seatPrice})</Text>}
            </Text>
          </View>
        )}
      </ScrollView>
    </Card>
  )
}

export default MinimalistSeatmap
