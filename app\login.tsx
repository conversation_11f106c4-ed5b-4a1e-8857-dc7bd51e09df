import {
    View,
    Text,
    KeyboardAvoidingView,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Platform,
    ActivityIndicator,
    Image,
    Alert,
} from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Eye, EyeOff, Lock, User } from "@/constants/icons";
import { useRouter } from "expo-router";
import { useAuthStore } from "@/stores/auth";
import { images } from "@/constants/images";
import { StatusBar } from "expo-status-bar";

const Login = () => {
    const router = useRouter();
    const { login, isLoading, error, setError } = useAuthStore();
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);

    const handleLogin = async () => {
        if (!email || !password) {
            return;
        }

        try {
            const status = await login(email, password);

            if (error) {
                Alert.alert("Login Error", error);
                return;
            }

            Alert.alert(
                "Login Status",
                status === "success" ? "Login successful!" : "Login failed!",
                [{ text: "OK" }]
            );

            if (status === "success") {
                router.replace("/(tabs)/home");
            }
        } catch (error) {
            console.error("Login error:", error);
            Alert.alert("Login Error", "An error occurred during login.");
        } finally {
            setError(null);
        }
    };

    const handleBack = () => {
        router.push("/onboarding");
    };

    return (
        <SafeAreaView className="flex-1 bg-white">
            <StatusBar style="inverted" />
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                className="flex-1"
            >
                <ScrollView
                    className="flex-1 px-6"
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 40 }}
                >
                    <TouchableOpacity
                        className="mt-2 p-2 rounded-full w-10 h-10 justify-center items-center bg-gray-100"
                        onPress={handleBack}
                    >
                        <ArrowLeft size={22} color="#1a365d" />
                    </TouchableOpacity>

                    <View className="items-center mt-8 mb-10">
                        <Image
                            source={images.logo}
                            className="w-32 h-32 mb-4 -mr-8 object-cover"
                            resizeMode="contain"
                        />
                        <Text className="text-2xl font-bold text-gray-800 mb-2">
                            Welcome Back
                        </Text>
                        <Text className="text-gray-500 text-center">
                            Sign in to your SomExpress account
                        </Text>
                    </View>

                    {/* Form */}
                    <View className="w-full">
                        <View className="flex-row items-center mb-4 rounded-xl px-4 h-14 bg-gray-50 border border-gray-200">
                            <User size={18} color="#64748b" />
                            <TextInput
                                className="flex-1 text-base text-gray-800 ml-2 "
                                placeholder="Email"
                                placeholderTextColor="#94a3b8"
                                value={email}
                                onChangeText={setEmail}
                                autoCapitalize="none"
                                keyboardType="email-address"
                            />
                        </View>

                        <View className="flex-row items-center rounded-xl px-4 h-14 bg-gray-50 border border-gray-200">
                            <Lock size={18} color="#64748b" />
                            <TextInput
                                className="flex-1 text-base text-gray-800 ml-2"
                                placeholder="Password"
                                placeholderTextColor="#94a3b8"
                                value={password}
                                onChangeText={setPassword}
                                secureTextEntry={!showPassword}
                            />
                            <TouchableOpacity
                                className="p-2"
                                onPress={() => setShowPassword(!showPassword)}
                            >
                                {showPassword ? (
                                    <EyeOff size={18} color="#64748b" />
                                ) : (
                                    <Eye size={18} color="#64748b" />
                                )}
                            </TouchableOpacity>
                        </View>

                        <TouchableOpacity className="self-end mt-3">
                            <Text className="text-blue-500 font-medium">
                                Forgot Password?
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`mt-4 rounded-xl h-14 justify-center items-center ${
                                isLoading ? "bg-blue-400" : "bg-blue-500"
                            }`}
                            onPress={handleLogin}
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <ActivityIndicator color="#ffffff" />
                            ) : (
                                <Text className="text-white text-base font-semibold">
                                    Sign In
                                </Text>
                            )}
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>

            {/* Footer */}
            <View className="flex-row justify-center items-center mt-4 mb-2">
                <Text className="text-gray-500">Powered by OrbitMindTech.</Text>
            </View>
        </SafeAreaView>
    );
};

export default Login;
