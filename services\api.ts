import apiClient from "./apiClient";

// Setup for auth token injection
const setupAuthInterceptors = (
    getToken: () => string | null,
    logout: () => void
) => {
    // Request interceptor
    apiClient.interceptors.request.use((config) => {
        const token = getToken();
        if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    });

    // Response interceptor
    apiClient.interceptors.response.use(
        (res) => res,
        (err) => {
            if (err.response?.status === 401) {
                logout();
            }
            return Promise.reject(err);
        }
    );
};

// Export the API client
const api = apiClient;
export default api;

export { setupAuthInterceptors };
