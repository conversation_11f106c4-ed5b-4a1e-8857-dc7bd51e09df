import { Wallet } from "@/types/wallet";
import { ChevronR<PERSON>, Eye, EyeOff } from "@/constants/icons";
import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";

const WalletCard = ({ walletInfo }: { walletInfo: Wallet }) => {
    const [showCardNumber, setShowCardNumber] = useState(false);

    const handleNavigate = () => {
        console.log("Navigating to Wallet Details");
    };

    const toggleCardNumber = () => {
        setShowCardNumber(!showCardNumber);
    };

    const formatCardNumber = (number: string) => {
        if (!number) return "";
        return showCardNumber ? number : `**** **** **** ${number.slice(-4)}`;
    };

    return (
        <View className="px-4 mt-2">
            <TouchableOpacity
                className="rounded-2xl overflow-hidden bg-white shadow-md"
                onPress={handleNavigate}
                activeOpacity={0.9}
            >
                {/* Card */}
                <View className="bg-indigo-600 rounded-2xl p-4 overflow-hidden">
                    <View className=" h-44 justify-between">
                        {/* Header */}
                        <View className="flex-row justify-between items-start">
                            <View>
                                <Text className="text-sm text-white/80 mb-1">
                                    Available Balance
                                </Text>
                                <Text className="text-2xl font-bold text-white">
                                    ${walletInfo.balance.toFixed(2)}
                                </Text>
                            </View>
                            <View className="bg-white/20 px-2.5 py-1 rounded-md">
                                <Text className="text-white font-bold text-xs uppercase">
                                    {walletInfo.provider}
                                </Text>
                            </View>
                        </View>

                        {/* Card Number */}
                        <View className="mt-4 flex-row items-center">
                            <Text className="text-white text-lg tracking-widest mr-2">
                                {formatCardNumber(walletInfo.cardNumber)}
                            </Text>
                            <TouchableOpacity
                                onPress={toggleCardNumber}
                                className="p-1"
                            >
                                {showCardNumber ? (
                                    <EyeOff
                                        size={18}
                                        color="rgba(255,255,255,0.8)"
                                    />
                                ) : (
                                    <Eye
                                        size={18}
                                        color="rgba(255,255,255,0.8)"
                                    />
                                )}
                            </TouchableOpacity>
                        </View>

                        {/* Footer */}
                        <View className="flex-row justify-between mt-4">
                            <View>
                                <Text className="text-xs text-white/70 mb-1">
                                    Card Holder
                                </Text>
                                <Text className="text-white font-medium">
                                    {walletInfo.cardHolder}
                                </Text>
                            </View>
                            <View>
                                <Text className="text-xs text-white/70 mb-1">
                                    Expires
                                </Text>
                                <Text className="text-white font-medium">
                                    09/35
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        </View>
    );
};

export default WalletCard;
