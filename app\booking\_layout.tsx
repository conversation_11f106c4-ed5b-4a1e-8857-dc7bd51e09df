import { ArrowLeft } from "@/constants/icons";
import { Stack, useRouter } from "expo-router";
import React from "react";
import { TouchableOpacity } from "react-native";

export default function BookingLayout() {
    const router = useRouter();

    return (
        <Stack>
            <Stack.Screen name="add" options={{ headerShown: false }} />

            <Stack.Screen
                name="[id]/details"
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="[id]/confirm"
                options={{
                    headerTitle: "Complete Reservation",
                    headerLeft: () => (
                        <TouchableOpacity
                            className="p-2 rounded-lg bg-gray-100"
                            onPress={() => router.back()}
                        >
                            <ArrowLeft size={20} color="#111827" />
                        </TouchableOpacity>
                    ),
                }}
            />
        </Stack>
    );
}
