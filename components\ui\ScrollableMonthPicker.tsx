import React from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { X } from "../../constants/icons";

interface ScrollableMonthPickerProps {
    months: string[];
    selectedMonthIndex: number;
    onMonthSelect: (monthIndex: number) => void;
    onClose: () => void;
}

export default function ScrollableMonthPicker({
    months,
    selectedMonthIndex,
    onMonthSelect,
    onClose,
}: ScrollableMonthPickerProps) {
    return (
        <View style={{ width: "100%" }}>
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: 16,
                    paddingBottom: 8,
                    borderBottomWidth: 1,
                    borderBottomColor: "#f1f5f9",
                }}
            >
                <Text
                    style={{
                        fontSize: 18,
                        fontWeight: "600",
                        color: "#0f172a",
                    }}
                >
                    Select Month
                </Text>
                <TouchableOpacity
                    style={{
                        padding: 8,
                        borderRadius: 20,
                        backgroundColor: "#f1f5f9",
                    }}
                    onPress={onClose}
                >
                    <X size={18} color="#64748b" />
                </TouchableOpacity>
            </View>

            <ScrollView
                style={{ maxHeight: 300 }}
                showsVerticalScrollIndicator={true}
            >
                <View
                    style={{
                        flexDirection: "row",
                        flexWrap: "wrap",
                        justifyContent: "center",
                        paddingBottom: 16,
                    }}
                >
                    {months.map((month, index) => (
                        <TouchableOpacity
                            key={month}
                            style={{
                                width: 90,
                                height: 50,
                                margin: 5,
                                padding: 10,
                                borderRadius: 8,
                                backgroundColor:
                                    selectedMonthIndex === index
                                        ? "#3b82f6"
                                        : "#f1f5f9",
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                            onPress={() => onMonthSelect(index)}
                        >
                            <Text
                                style={{
                                    fontSize: 14,
                                    fontWeight: "500",
                                    color:
                                        selectedMonthIndex === index
                                            ? "white"
                                            : "#0f172a",
                                }}
                            >
                                {month}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
        </View>
    );
}
