import { Colors } from "@/constants/colors";
import React from "react";
import {
    StyleSheet,
    Text,
    TextInput,
    TextInputProps,
    TouchableOpacity,
    View,
    ViewStyle,
    StyleProp,
    TextStyle,
} from "react-native";

interface InputProps extends Omit<TextInputProps, "style"> {
    label?: string;
    error?: string;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    onRightIconPress?: () => void;
    style?: StyleProp<TextStyle>;
    containerStyle?: StyleProp<ViewStyle>;
}

export const Input: React.FC<InputProps> = ({
    style,
    containerStyle,
    label,
    error,
    leftIcon,
    rightIcon,
    onRightIconPress,
    ...props
}) => {
    return (
        <View style={styles.container}>
            {label && <Text style={styles.label}>{label}</Text>}

            <View
                style={[
                    styles.inputContainer,
                    error ? styles.inputError : null,
                    props.editable === false ? styles.inputDisabled : null,
                    containerStyle,
                ]}
            >
                {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}

                <TextInput
                    style={[
                        styles.input,
                        leftIcon ? styles.inputWithLeftIcon : null,
                        rightIcon ? styles.inputWithRightIcon : null,
                        style,
                    ]}
                    placeholderTextColor="#9ca3af"
                    {...props}
                />

                {rightIcon && (
                    <TouchableOpacity
                        style={styles.rightIcon}
                        onPress={onRightIconPress}
                        disabled={!onRightIconPress}
                    >
                        {rightIcon}
                    </TouchableOpacity>
                )}
            </View>

            {error && <Text style={styles.errorText}>{error}</Text>}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginBottom: 16,
    },
    label: {
        marginBottom: 6,
        fontSize: 14,
        fontWeight: "500",
        color: Colors.light.text,
    },
    inputContainer: {
        flexDirection: "row",
        alignItems: "center",
        borderWidth: 0,
        borderColor: Colors.light.border,
        borderRadius: 8,
        backgroundColor: "#fff",
    },
    input: {
        flex: 1,
        paddingVertical: 10,
        paddingHorizontal: 12,
        fontSize: 16,
        color: Colors.light.text,
    },
    inputWithLeftIcon: {
        paddingLeft: 8,
    },
    inputWithRightIcon: {
        paddingRight: 8,
    },
    leftIcon: {
        paddingLeft: 12,
    },
    rightIcon: {
        paddingRight: 12,
    },
    inputError: {
        borderColor: Colors.light.error,
    },
    inputDisabled: {
        backgroundColor: "#f9fafb",
        opacity: 0.7,
    },
    errorText: {
        color: Colors.light.error,
        fontSize: 12,
        marginTop: 4,
    },
});
