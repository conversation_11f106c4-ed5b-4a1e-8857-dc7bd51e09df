import React from "react"
import { StyleSheet, Text, TextProps } from "react-native"

interface LabelProps extends TextProps {
  htmlFor?: string // This won't be used in React Native but kept for API compatibility
}

export function Label({ style, children, ...props }: LabelProps) {
  return (
    <Text style={[styles.label, style]} {...props}>
      {children}
    </Text>
  )
}

const styles = StyleSheet.create({
  label: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 6,
    color: "#0f172a", // slate-900
  },
})