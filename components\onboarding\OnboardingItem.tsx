import React from "react";
import {
    View,
    Text,
    useWindowDimensions,
    ActivityIndicator,
} from "react-native";
import { Image } from "@rneui/themed";

interface OnboardingItemProps {
    item: {
        id: string;
        title: string;
        description: string;
        image: string;
    };
}

export const OnboardingItem: React.FC<OnboardingItemProps> = ({ item }) => {
    const { width } = useWindowDimensions();

    return (
        <View
            className={`flex-1 justify-center items-center bg-white`}
            style={{ width }}
        >
            <Image
                source={item.image as any}
                className="h-[300px] justify-center mb-[30px]"
                style={{
                    width: width * 0.9,
                    height: width * 0.9,
                    resizeMode: "contain",
                }}
                PlaceholderContent={<ActivityIndicator color="#000" />}
                placeholderStyle={{
                    backgroundColor: "transparent",
                }}
            />

            <View className="px-[30px] items-center">
                <Text className="font-extrabold text-[28px] mb-4 text-blue-600 text-center">
                    {item.title}
                </Text>
                <Text className="font-normal text-base text-[#003087] text-center px-5 leading-6">
                    {item.description}
                </Text>
            </View>
        </View>
    );
};
