import React, { useEffect, useRef } from "react";
import { Animated, FlatList, Text, TouchableOpacity, View } from "react-native";
import { DateOption } from "../../types/flight";

interface DateSliderProps {
    dates: DateOption[];
    selectedDate: string;
    onDateSelect: (date: string) => void;
}

export default function DateSlider({
    dates,
    selectedDate,
    onDateSelect,
}: DateSliderProps) {
    const flatListRef = useRef<FlatList>(null);
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const itemWidth = 96; // w-24 = 96px

    // Auto-scroll to selected date when component mounts or selection changes
    useEffect(() => {
        const selectedIndex = dates.findIndex(
            (item) => item.date === selectedDate
        );
        if (selectedIndex !== -1 && flatListRef.current) {
            flatListRef.current.scrollToIndex({
                index: selectedIndex,
                animated: true,
                viewPosition: 0.5, // Center the item
            });
        }

        // Fade-in animation
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
        }).start();
    }, [selectedDate, dates]);

    // Handle scroll error (if index is out of bounds)
    const onScrollToIndexFailed = (info: {
        index: number;
        highestMeasuredFrameIndex: number;
        averageItemLength: number;
    }) => {
        const wait = new Promise((resolve) => setTimeout(resolve, 100));
        wait.then(() => {
            flatListRef.current?.scrollToIndex({
                index: info.index,
                animated: true,
                viewPosition: 0.5,
            });
        });
    };

    const renderDateItem = ({
        item,
        index,
    }: {
        item: DateOption;
        index: number;
    }) => {
        const date = new Date(item.date);
        const isSelected = item.date === selectedDate;
        const isBestDeal = item.isBestDeal;
        const month = new Intl.DateTimeFormat("en-US", {
            month: "short",
        }).format(date);
        const day = date.getDate();
        const weekday = new Intl.DateTimeFormat("en-US", {
            weekday: "short",
        }).format(date);

        return (
            <Animated.View style={{ opacity: fadeAnim }}>
                <TouchableOpacity
                    className={`relative mr-2 w-24 overflow-hidden rounded-xl ${
                        isSelected
                            ? "border-2 border-blue-600"
                            : "border border-gray-200"
                    } ${isBestDeal && !isSelected ? "border-blue-400" : ""}`}
                    onPress={() => item.available && onDateSelect(item.date)}
                    disabled={!item.available}
                    activeOpacity={0.7}
                    style={{
                        transform: [{ scale: isSelected ? 1.02 : 1 }],
                        shadowColor: isSelected ? "#3B82F6" : "#000",
                        shadowOffset: { width: 0, height: isSelected ? 3 : 1 },
                        shadowOpacity: isSelected ? 0.3 : 0.1,
                        shadowRadius: isSelected ? 5 : 2,
                        elevation: isSelected ? 5 : 2,
                        opacity: !item.available ? 0.7 : 1,
                    }}
                >
                    {isBestDeal && (
                        <View className="absolute -right-8 -top-1 z-10 rotate-45 bg-blue-500 px-6 py-0.5">
                            <Text className="text-[8px] font-bold text-white">
                                BEST DEAL
                            </Text>
                        </View>
                    )}
                    <View
                        className={`w-full px-2 py-2 ${
                            isSelected
                                ? "bg-blue-100"
                                : isBestDeal
                                ? "bg-blue-50"
                                : "bg-gray-100"
                        }`}
                    >
                        <Text
                            className={`text-center text-xs font-bold ${
                                isSelected ? "text-blue-700" : "text-gray-700"
                            }`}
                        >
                            {weekday}
                        </Text>
                    </View>
                    <View
                        className={`items-center px-2 py-3 ${
                            isSelected ? "bg-white" : "bg-white"
                        }`}
                    >
                        <Text
                            className={`text-base font-bold ${
                                isSelected ? "text-blue-800" : "text-gray-800"
                            }`}
                        >
                            {month} {day}
                        </Text>
                        {item.price !== undefined && item.price !== null && (
                            <Text
                                className={`mt-1 text-sm font-bold ${
                                    isBestDeal
                                        ? "text-blue-600"
                                        : "text-gray-700"
                                }`}
                            >
                                $
                                {typeof item.price === "string"
                                    ? parseFloat(item.price)
                                    : item.price}
                            </Text>
                        )}
                        {item.available ? (
                            <View className="mt-2 flex-row items-center rounded-full bg-green-100 px-2 py-1">
                                <View className="mr-1 h-2 w-2 rounded-full bg-green-600"></View>
                                <Text className="text-xs font-medium text-green-700">
                                    Available
                                </Text>
                            </View>
                        ) : (
                            <View className="mt-2 flex-row items-center rounded-full bg-gray-100 px-2 py-1">
                                <View className="mr-1 h-2 w-2 rounded-full bg-gray-400"></View>
                                <Text className="text-xs font-medium text-gray-500">
                                    Unavailable
                                </Text>
                            </View>
                        )}
                    </View>
                </TouchableOpacity>
            </Animated.View>
        );
    };

    return (
        <View className="rounded-xl border border-gray-200 bg-white p-3 shadow-sm">
            <View className="mb-2 flex-row items-center justify-between">
                <Text className="text-base font-semibold text-gray-800">
                    Available Dates
                </Text>
                <Text className="text-xs font-medium text-blue-600">
                    Swipe to see more
                </Text>
            </View>
            <FlatList
                ref={flatListRef}
                data={dates}
                renderItem={renderDateItem}
                keyExtractor={(item) => item.date}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerClassName="py-1"
                snapToInterval={itemWidth + 8} // Width + margin
                snapToAlignment="start"
                decelerationRate="fast"
                onScrollToIndexFailed={onScrollToIndexFailed}
                initialNumToRender={5}
                maxToRenderPerBatch={8}
                windowSize={5}
            />
        </View>
    );
}
