import { useQuery } from '@tanstack/react-query';
import api from '../services/api';

export const useLocations = () => {
  const fetchLocations = async () => {
    try {
      const response = await api.get('/setup/location/all');
      return response.data;
    } catch (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }
  };

  return useQuery({
    queryKey: ['locations'],
    queryFn: fetchLocations,
  });
};