import api from "@/services/api";
import { create } from "zustand";

export interface ClassOption {
    class: string;
    availableSeats: number;
    hasEnoughSeats: boolean;
    pricing: {
        adult: number;
        child: number;
        infant: number;
    };
    isFull?: boolean;
}

export interface Segment {
    _id: string;
    schedule: {
        departure: {
            date: string;
            time: string;
        };
        arrival: {
            date: string;
            time: string;
        };
        duration: number;
    };
    route: {
        origin: {
            city: string;
            airport?: string;
        };
        destination: {
            city: string;
            airport?: string;
        };
    };
    cabinOptions: ClassOption[];
    status?: string;
    checkIn?: {
        openTime: string;
        closeTime: string;
    };
    boardingGateCloseTime?: string;
}

export interface Flight {
    segments: Segment[];
    cashPayment: boolean;
    flightNumber: string;
    aircraft: {
        model: string;
    };
    services: {
        seatSelection: boolean;
        inFlightMeal: boolean;
        priorityBoarding: boolean;
        extraBaggage: boolean;
    }[];
}

export interface DateOption {
    date: string;
    available: boolean;
    isBestDeal?: boolean;
    price?: number | string;
}

export interface SearchParams {
    from: string;
    to: string;
    date: string;
    passengers: number;
}

export interface SearchResponse {
    success: boolean;
    flights?: Flight[];
    suggestions?: {
        dates?: DateOption[];
    };
    message?: string;
}

export interface FlightState {
    flights: Record<string, Flight[]>;
    dateOptions: DateOption[];
    loading: boolean;
    error: string | null;
    systemSettings: any;
    searchFlights: (params: SearchParams) => Promise<SearchResponse>;
    fetchSystemSettings: () => Promise<any>;
}

export const useFlightStore = create<FlightState>((set, get) => ({
    flights: {},
    dateOptions: [],
    loading: false,
    error: null,
    systemSettings: {},

    searchFlights: async (params: SearchParams) => {
        set({ loading: true, error: null });
        try {
            const response = await api.get("/setup/flight/customer-search", {
                params,
            });

            if (response.data.success && response.data.flights?.length) {
                const flightKey = `${params.from}-${params.to}-${params.date}`;
                set((state) => ({
                    flights: {
                        ...state.flights,
                        [flightKey]: response.data.flights,
                    },
                    dateOptions: response.data.suggestions?.dates || [],
                    loading: false,
                }));
            } else if (response.data.suggestions?.dates?.length) {
                set({
                    dateOptions: response.data.suggestions.dates,
                    loading: false,
                });
            } else {
                set({
                    error: "No flights found for the selected dates.",
                    loading: false,
                });
            }

            return response.data;
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "An error occurred while searching flights";
            set({ error: errorMessage, loading: false });
            throw new Error(errorMessage);
        }
    },

    fetchSystemSettings: async () => {
        try {
            const response = await api.get("/settings");
            set({ systemSettings: response.data });
            return response.data;
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "An error occurred while fetching system settings";
            set({ error: errorMessage });
            throw new Error(errorMessage);
        }
    },
}));
