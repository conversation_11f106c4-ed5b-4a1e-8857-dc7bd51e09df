import {
    Armchair,
    ChevronLeft,
    CreditCard,
    FileText,
    Plane,
    User,
} from "@/constants/icons";
import { useRouter } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Toast } from "react-native-toast-message/lib/src/Toast";

// Components
import BookingReview from "@/components/booking/BookingReview";
import FlightSummary from "@/components/booking/FlightSummary";
import PassengerDetails from "@/components/booking/PassengerDetails";
import PaymentDetails from "@/components/booking/PaymentDetails";
import SeatSelection from "@/components/booking/SeatSelection";
import Stepper from "@/components/booking/Stepper";
import { Button } from "@/components/ui/button";
import { useBookingStore } from "@/stores/booking-store";

export default function BookingScreen() {
    const { bookingForm } = useBookingStore();
    const [currentStep, setCurrentStep] = useState(0);
    const [isFormValid, setIsFormValid] = useState(true);
    const [isPaymentValid, setIsPaymentValid] = useState(false);
    const router = useRouter();

    useEffect(() => {
        if (
            !bookingForm ||
            !bookingForm.flightInfo ||
            !bookingForm.flightInfo.flights ||
            bookingForm.flightInfo.flights.length === 0
        ) {
            router.replace("/(tabs)/home");
        }
    }, [bookingForm, router]);

    const handleValidationChange = useCallback((isValid: boolean) => {
        setIsFormValid(isValid);
    }, []);

    const handlePaymentValidationChange = useCallback((isValid: boolean) => {
        setIsPaymentValid(isValid);
    }, []);

    const bookingSteps = [
        { name: "Flight Summary", icon: Plane },
        { name: "Passenger Information", icon: User },
        { name: "Seat Selection", icon: Armchair },
        { name: "Review", icon: FileText },
        { name: "Payment", icon: CreditCard },
    ];

    const handlePaymentComplete = useCallback(
        (success: boolean, data: any) => {
            if (success) {
                Toast.show({
                    type: "success",
                    text1: "Payment and booking completed successfully",
                    text2: data?.booking?.PNR
                        ? `PNR: ${data.booking.PNR}`
                        : "Your booking has been confirmed",
                    position: "top",
                    visibilityTime: 1500,
                });
                router.push("/(tabs)/bookings");
            } else {
                Toast.show({
                    type: "error",
                    text1: "Payment and booking failed",
                    text2: data?.message || "Please try again",
                    position: "top",
                    visibilityTime: 1500,
                });
            }
        },
        [router]
    );

    const handleStepClick = (stepIndex: number) => {
        if (stepIndex < currentStep) {
            setCurrentStep(stepIndex);
        }
    };

    const nextStep = () => {
        if (!bookingForm?.flightInfo?.flights) {
            Toast.show({
                type: "error",
                text1: "Missing flight information",
                text2: "Please select a flight",
                position: "top",
                visibilityTime: 1500,
            });
            return;
        }

        if (currentStep === 0 && bookingForm.flightInfo.flights.length === 0) {
            Toast.show({
                type: "error",
                text1: "Please select a flight",
                text2: "Please select a flight",
                position: "top",
                visibilityTime: 1500,
            });
            return;
        }

        if (currentStep === 1 && !isFormValid) {
            Toast.show({
                type: "error",
                text1: "Please fill in all required fields *",
                text2: "There are some errors in the form",
                position: "top",
                visibilityTime: 1500,
            });
            return;
        }

        setCurrentStep((prev) => Math.min(prev + 1, 4));
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 0));
    };

    if (
        !bookingForm ||
        !bookingForm.flightInfo ||
        !bookingForm.flightInfo.flights
    ) {
        return (
            <SafeAreaView className="flex-1 bg-white">
                <View className="flex-1 justify-center items-center">
                    <Text className="text-base text-gray-500">
                        Loading booking information...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return <FlightSummary />;
            case 1:
                return (
                    <PassengerDetails
                        onValidationChange={handleValidationChange}
                    />
                );
            case 2:
                return <SeatSelection />;
            case 3:
                return <BookingReview />;
            case 4:
                return (
                    <PaymentDetails
                        onPaymentValidation={handlePaymentValidationChange}
                        onPaymentComplete={handlePaymentComplete}
                        isReservationPayment={false}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <SafeAreaView className="flex-1 bg-white" edges={["top"]}>
            {/* Header */}
            <View className="flex-row items-center px-4 py-3 border-b border-gray-200">
                <TouchableOpacity
                    onPress={() => router.back()}
                    className="mr-4"
                >
                    <ChevronLeft size={24} color="#3b82f6" />
                </TouchableOpacity>
                <Text className="text-lg font-semibold text-black">
                    Book Your Flight - {bookingSteps[currentStep].name}
                </Text>
            </View>

            {/* Content */}
            <View className="flex-1 bg-white">
                <Stepper
                    current={currentStep}
                    steps={bookingSteps}
                    onStepClick={handleStepClick}
                />
                <ScrollView
                    className="flex-1 mt-5 px-4"
                    contentContainerStyle={{ paddingBottom: 20 }}
                >
                    {renderStepContent()}
                </ScrollView>

                {/* Footer Buttons */}
                <View className="mt-4 mb-10 flex-row justify-between max-w-[600px] w-full self-center px-6">
                    <Button
                        onPress={prevStep}
                        disabled={currentStep === 0}
                        variant="secondary"
                        className="bg-transparent px-4 py-2 border border-gray-300"
                    >
                        <Text className="text-blue-700">Previous</Text>
                    </Button>
                    {currentStep < 4 && (
                        <Button
                            onPress={nextStep}
                            className="bg-blue-600 px-4 py-2 border border-gray-300"
                        >
                            <Text className="text-white">Next</Text>
                        </Button>
                    )}
                </View>
            </View>
        </SafeAreaView>
    );
}
