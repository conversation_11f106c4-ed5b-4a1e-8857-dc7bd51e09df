import { Button } from "@/components/ui/button";
import { Save } from "@/constants/icons";
import { useBookingStore } from "@/stores/booking-store";
import { usePaymentStore } from "@/stores/payment-store";
import { useWalletStore } from "@/stores/wallet-store";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import { ActivityIndicator, Text, View } from "react-native";
import Toast from "react-native-toast-message";

interface ReservationSectionProps {
    processingReservation: boolean;
    setProcessingReservation: (value: boolean) => void;
    onPaymentComplete?: (success: boolean, data?: any) => void;
}

export function ReservationSection({
    processingReservation,
    setProcessingReservation,
    onPaymentComplete,
}: ReservationSectionProps) {
    const { bookingForm, setBookingForm } = useBookingStore();
    const { createReservation } = usePaymentStore();
    const { getMyWallet } = useWalletStore();

    const { data: walletData } = useQuery({
        queryKey: ["mywallet"],
        queryFn: getMyWallet,
    });

    // Reservation mutation (no payment)
    const reservationMutation = useMutation({
        mutationFn: () => {
            const payload = {
                bookingData: {
                    amount: Number(bookingForm.paymentInfo.totalAmount),
                    userId: walletData?.userId,
                    flightInfo: bookingForm.flightInfo,
                    passengersDetails: bookingForm.passengersDetails,
                    taxAmount: bookingForm.paymentInfo.taxAmount,
                    totalAmount: bookingForm.paymentInfo.totalAmount,
                    paymentMethod: bookingForm.paymentInfo.paymentMethod,
                    paymentStatus: "pending",
                    charges: bookingForm.paymentInfo.charges,
                    paymentCurrency:
                        bookingForm.paymentInfo.paymentCurrency || "USD",
                    bookingStatus: "reserved",
                },
            };
            return createReservation(payload);
        },
        onSuccess: (data) => {
            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "pending", // No payment processed yet
                },
                bookingReference: {
                    bookingId: data.booking?._id,
                    bookingNumber: data.booking?.bookingNumber,
                    PNR: data.booking?.PNR,
                    expiresAt: data.booking?.expiresAt,
                },
                bookingStatus: "reserved",
            }));

            Toast.show({
                type: "success",
                text1: "Reservation created successfully!",
                text2: "Valid for 24 hours.",
            });

            if (onPaymentComplete) {
                onPaymentComplete(true, data);
            }
        },
        onError: (error) => {
            console.error("Reservation failed:", error);
            setBookingForm((prev) => ({
                ...prev,
                paymentInfo: {
                    ...prev.paymentInfo,
                    paymentStatus: "pending",
                },
                bookingStatus: "cancelled",
            }));

            Toast.show({
                type: "error",
                text1: "Reservation failed.",
                text2: "Please try again.",
            });

            if (onPaymentComplete) {
                onPaymentComplete(false, error);
            }
        },
        onSettled: () => {
            setProcessingReservation(false);
        },
    });

    // Process reservation (no payment)
    const processReservation = () => {
        setProcessingReservation(true);
        reservationMutation.mutate();
    };

    return (
        <View className="rounded-xl p-5 bg-blue-50">
            <View className="flex-row items-center justify-between">
                <View className="flex-row items-center gap-3">
                    <View className="bg-blue-100 p-3 rounded-full">
                        <Save size={16} color="#2563eb" />
                    </View>
                    <View>
                        <Text className="text-md font-semibold">
                            Reserve Now
                        </Text>
                    </View>
                </View>
                <Button
                    onPress={processReservation}
                    disabled={processingReservation}
                    variant="outline"
                    className="h-12 border-blue-600"
                >
                    {processingReservation ? (
                        <View className="flex-row items-center">
                            <ActivityIndicator
                                size="small"
                                color="#2563eb"
                                className="mr-2"
                            />
                            <Text className="text-blue-600 font-semibold text-sm">
                                Processing...
                            </Text>
                        </View>
                    ) : (
                        <View className="flex-row items-center">
                            <Save size={16} color="#2563eb" className="mr-2" />
                            <Text className="text-blue-600 font-semibold">
                                Reserve
                            </Text>
                        </View>
                    )}
                </Button>
            </View>
        </View>
    );
}
