import React, { ReactNode } from "react";
import { View, Text, Platform } from "react-native";
import Animated, { FadeIn } from "react-native-reanimated";

interface ProfileSectionProps {
    title: string;
    children: ReactNode;
    icon?: ReactNode;
    delay?: number;
}

export const ProfileSection: React.FC<ProfileSectionProps> = ({
    title,
    children,
    icon,
    delay = 0,
}) => {
    const AnimatedComponent = Platform.OS !== "web" ? Animated.View : View;
    const animationProps =
        Platform.OS !== "web"
            ? { entering: FadeIn.delay(delay).duration(400) }
            : {};

    return (
        <AnimatedComponent
            className="bg-white rounded-lg mb-3 shadow-sm overflow-hidden border border-gray-200"
            {...animationProps}
        >
            <View className="flex-row items-center px-4 pt-4 pb-3 border-b border-gray-200">
                {icon && <View className="mr-2">{icon}</View>}
                <Text className="text-base font-semibold text-gray-800">
                    {title}
                </Text>
            </View>
            <View className="px-4 py-3">{children}</View>
        </AnimatedComponent>
    );
};
