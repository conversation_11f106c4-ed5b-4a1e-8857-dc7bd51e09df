import { Calendar, ChevronDown, X } from "../../constants/icons";
import React, { useState } from "react";
import {
    Modal,
    Pressable,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import ScrollableYearPicker from "./ScrollableYearPicker";
import ScrollableMonthPicker from "./ScrollableMonthPicker";

interface DatePickerProps {
    date: Date;
    setDate: (date: Date) => void;
    label?: string;
    placeholder?: string;
    startYear?: number;
    endYear?: number;
    style?: any;
}

export default function DatePicker({
    date,
    setDate,
    label,
    placeholder = "Select date",
    startYear = 1900,
    endYear = new Date().getFullYear() + 10,
    style,
}: DatePickerProps) {
    const [show, setShow] = useState(false);
    const [showYearPicker, setShowYearPicker] = useState(false);
    const [showMonthPicker, setShowMonthPicker] = useState(false);

    // Fix timezone issues by ensuring we use local date
    const normalizeDate = (date: Date | string): Date => {
        if (typeof date === "string") {
            // Parse date string to ensure it's in local timezone
            const [year, month, day] = date
                .split("T")[0]
                .split("-")
                .map(Number);
            return new Date(year, month - 1, day, 12, 0, 0);
        }

        // Clone the date and set to noon to avoid timezone issues
        const normalized = new Date(date);
        normalized.setHours(12, 0, 0, 0);
        return normalized;
    };

    const [selectedDate, setSelectedDate] = useState(() =>
        normalizeDate(date || new Date())
    );
    const [selectedMonth, setSelectedMonth] = useState(() =>
        normalizeDate(date || new Date())
    );

    // Generate years for the year picker - limit to a reasonable range
    const generateYears = () => {
        // For DOB pickers, show years from startYear to endYear
        // For future date pickers (like passport expiry), focus on more recent years
        const isDateOfBirth = endYear <= new Date().getFullYear() + 10;

        if (isDateOfBirth) {
            // For DOB, show a reasonable range with most recent years first
            return Array.from(
                { length: Math.min(endYear - startYear + 1, 150) }, // Limit to 150 years max
                (_, i) => endYear - i
            );
        } else {
            // For future dates, focus on next 30 years
            const currentYear = new Date().getFullYear();
            return Array.from({ length: 30 }, (_, i) => currentYear + i);
        }
    };

    const years = generateYears();

    // Format date to display
    const formatDate = (date: Date) => {
        return date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
        });
    };

    // Show date picker
    const showDatepicker = () => {
        setShow(true);
        setSelectedDate(normalizeDate(date || new Date()));
        setSelectedMonth(normalizeDate(date || new Date()));
        setShowYearPicker(false);
        setShowMonthPicker(false);
    };

    // Handle date selection
    const handleDateSelect = () => {
        setDate(selectedDate);
        setShow(false);
    };

    // Handle year selection
    const handleYearSelect = (year: number) => {
        const newDate = new Date(selectedMonth);
        newDate.setFullYear(year);
        setSelectedMonth(newDate);
        setShowYearPicker(false);
        setShowMonthPicker(true); // Show month picker after selecting year
    };

    // Handle month selection
    const handleMonthSelect = (monthIndex: number) => {
        const newDate = new Date(selectedMonth);
        newDate.setMonth(monthIndex);
        setSelectedMonth(newDate);
        setShowMonthPicker(false);
    };

    // Get days in month
    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month + 1, 0).getDate();
    };

    // Get day names
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    // Get month names
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    // Generate calendar days
    const generateCalendarDays = () => {
        const year = selectedMonth.getFullYear();
        const monthIndex = selectedMonth.getMonth();
        const daysInMonth = getDaysInMonth(year, monthIndex);
        const firstDayOfMonth = new Date(year, monthIndex, 1).getDay();

        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDayOfMonth; i++) {
            days.push({ day: "", disabled: true });
        }

        // Add days of the month
        for (let i = 1; i <= daysInMonth; i++) {
            const currentDate = new Date(year, monthIndex, i, 12, 0, 0);
            const minDate = startYear ? new Date(startYear, 0, 1) : null;
            const maxDate = endYear ? new Date(endYear, 11, 31) : null;

            const isDisabled =
                (minDate && currentDate < minDate) ||
                (maxDate && currentDate > maxDate);

            const isToday =
                currentDate.getDate() === new Date().getDate() &&
                currentDate.getMonth() === new Date().getMonth() &&
                currentDate.getFullYear() === new Date().getFullYear();

            days.push({
                day: i,
                date: currentDate,
                disabled: isDisabled,
                isToday,
            });
        }

        return days;
    };

    // Handle month navigation
    const goToPreviousMonth = () => {
        const newMonth = new Date(selectedMonth);
        newMonth.setMonth(newMonth.getMonth() - 1);

        // Don't allow going before the minimum date's month
        if (startYear && newMonth.getFullYear() < startYear) {
            return;
        }

        setSelectedMonth(newMonth);
    };

    const goToNextMonth = () => {
        const newMonth = new Date(selectedMonth);
        newMonth.setMonth(newMonth.getMonth() + 1);

        // Don't allow going after the maximum date's month
        if (endYear && newMonth.getFullYear() > endYear) {
            return;
        }

        setSelectedMonth(newMonth);
    };

    const calendarDays = generateCalendarDays();

    const renderYearPicker = () => {
        return (
            <ScrollableYearPicker
                years={years}
                selectedYear={selectedMonth.getFullYear()}
                onYearSelect={(year) => {
                    handleYearSelect(year);
                }}
                onClose={() => setShowYearPicker(false)}
            />
        );
    };

    // Render month picker using the ScrollableMonthPicker component
    const renderMonthPicker = () => {
        return (
            <ScrollableMonthPicker
                months={monthNames}
                selectedMonthIndex={selectedMonth.getMonth()}
                onMonthSelect={(monthIndex) => {
                    handleMonthSelect(monthIndex);
                }}
                onClose={() => setShowMonthPicker(false)}
            />
        );
    };

    return (
        <View style={[styles.container, style]}>
            {label && <Text style={styles.label}>{label}</Text>}

            <TouchableOpacity
                style={styles.pickerButton}
                onPress={showDatepicker}
                activeOpacity={0.7}
            >
                <Calendar size={20} color="#3b82f6" style={styles.icon} />
                <Text style={styles.dateText}>
                    {date ? formatDate(date) : placeholder}
                </Text>
            </TouchableOpacity>

            <Modal
                visible={show}
                transparent
                animationType="fade"
                onRequestClose={() => setShow(false)}
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setShow(false)}
                >
                    <View
                        style={{
                            width: "90%",
                            maxWidth: 340,
                            backgroundColor: "white",
                            borderRadius: 12,
                            padding: 16,
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.25,
                            shadowRadius: 3.84,
                            elevation: 5,
                            maxHeight: "80%", // Limit height to ensure it fits on screen
                        }}
                        onStartShouldSetResponder={() => true}
                    >
                        {showYearPicker ? (
                            renderYearPicker()
                        ) : showMonthPicker ? (
                            renderMonthPicker()
                        ) : (
                            <>
                                <View style={styles.modalHeader}>
                                    <Text style={styles.modalTitle}>
                                        Select Date
                                    </Text>
                                    <TouchableOpacity
                                        style={styles.closeButton}
                                        onPress={() => setShow(false)}
                                    >
                                        <X size={18} color="#64748b" />
                                    </TouchableOpacity>
                                </View>

                                <TouchableOpacity
                                    style={styles.yearMonthSelector}
                                    onPress={() => {
                                        setShowYearPicker(true);
                                    }}
                                >
                                    <Text style={styles.monthYearText}>
                                        {monthNames[selectedMonth.getMonth()]}{" "}
                                        {selectedMonth.getFullYear()}
                                    </Text>
                                    <ChevronDown size={16} color="#64748b" />
                                </TouchableOpacity>

                                <View style={styles.navigationButtons}>
                                    <TouchableOpacity
                                        onPress={goToPreviousMonth}
                                        style={styles.navButton}
                                    >
                                        <Text style={styles.navButtonText}>
                                            ←
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={goToNextMonth}
                                        style={styles.navButton}
                                    >
                                        <Text style={styles.navButtonText}>
                                            →
                                        </Text>
                                    </TouchableOpacity>
                                </View>

                                <View style={styles.daysHeader}>
                                    {dayNames.map((day) => (
                                        <Text key={day} style={styles.dayName}>
                                            {day}
                                        </Text>
                                    ))}
                                </View>

                                <View style={styles.calendarGrid}>
                                    {calendarDays.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={[
                                                styles.dayCell,
                                                item.date &&
                                                    selectedDate &&
                                                    item.date.toDateString() ===
                                                        selectedDate.toDateString() &&
                                                    styles.selectedDay,
                                                item.isToday &&
                                                    styles.todayCell,
                                                item.disabled &&
                                                    styles.disabledDay,
                                            ]}
                                            onPress={() => {
                                                if (
                                                    item.date &&
                                                    !item.disabled
                                                ) {
                                                    setSelectedDate(item.date);
                                                }
                                            }}
                                            disabled={item.disabled === true}
                                        >
                                            <Text
                                                style={[
                                                    styles.dayText,
                                                    item.date &&
                                                        selectedDate &&
                                                        item.date.toDateString() ===
                                                            selectedDate.toDateString() &&
                                                        styles.selectedDayText,
                                                    item.isToday &&
                                                        styles.todayText,
                                                    item.disabled &&
                                                        styles.disabledDayText,
                                                ]}
                                            >
                                                {item.day}
                                            </Text>
                                        </TouchableOpacity>
                                    ))}
                                </View>

                                <View style={styles.modalFooter}>
                                    <TouchableOpacity
                                        style={styles.cancelButton}
                                        onPress={() => setShow(false)}
                                    >
                                        <Text style={styles.cancelButtonText}>
                                            Cancel
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.confirmButton}
                                        onPress={handleDateSelect}
                                    >
                                        <Text style={styles.confirmButtonText}>
                                            Confirm
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        )}
                    </View>
                </Pressable>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        width: "100%",
    },
    label: {
        fontSize: 14,
        fontWeight: "500",
        marginBottom: 6,
        color: "#0f172a",
    },
    pickerButton: {
        flexDirection: "row",
        alignItems: "center",
        borderWidth: 1,
        borderColor: "#e2e8f0",
        borderRadius: 6,
        paddingHorizontal: 12,
        paddingVertical: 10,
        backgroundColor: "white",
    },
    icon: {
        marginRight: 8,
    },
    dateText: {
        fontSize: 14,
        color: "#0f172a",
    },
    modalOverlay: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
        width: "90%",
        maxWidth: 340,
        backgroundColor: "white",
        borderRadius: 12,
        padding: 16,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
        paddingBottom: 8,
        borderBottomWidth: 1,
        borderBottomColor: "#f1f5f9",
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: "600",
        color: "#0f172a",
    },
    closeButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: "#f1f5f9",
    },
    calendarHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 12,
    },
    yearMonthSelector: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f1f5f9",
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginBottom: 12,
        alignSelf: "center",
    },
    navigationButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 16,
    },
    navButton: {
        padding: 8,
        backgroundColor: "#f1f5f9",
        borderRadius: 20,
        width: 40,
        height: 40,
        justifyContent: "center",
        alignItems: "center",
    },
    navButtonText: {
        fontSize: 18,
        color: "#3b82f6",
        fontWeight: "600",
    },
    monthYearText: {
        fontSize: 16,
        fontWeight: "600",
        color: "#0f172a",
        marginRight: 4,
    },
    daysHeader: {
        flexDirection: "row",
        marginBottom: 8,
        backgroundColor: "#f8fafc",
        borderRadius: 8,
        padding: 4,
    },
    dayName: {
        flex: 1,
        textAlign: "center",
        fontSize: 12,
        fontWeight: "500",
        color: "#64748b",
    },
    calendarGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        marginBottom: 16,
        backgroundColor: "#fff",
        borderRadius: 8,
        padding: 4,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    dayCell: {
        width: "14.28%",
        aspectRatio: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: 2,
    },
    dayText: {
        fontSize: 14,
        color: "#0f172a",
    },
    selectedDay: {
        backgroundColor: "#3b82f6",
        borderRadius: 20,
        width: 36,
        height: 36,
        justifyContent: "center",
        alignItems: "center",
    },
    selectedDayText: {
        color: "white",
        fontWeight: "600",
    },
    todayCell: {
        backgroundColor: "#eff6ff",
        borderRadius: 20,
        width: 36,
        height: 36,
        justifyContent: "center",
        alignItems: "center",
    },
    todayText: {
        color: "#3b82f6",
        fontWeight: "600",
    },
    disabledDay: {
        opacity: 0.3,
    },
    disabledDayText: {
        color: "#94a3b8",
    },
    modalFooter: {
        flexDirection: "row",
        justifyContent: "flex-end",
        marginTop: 8,
    },
    cancelButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        marginRight: 8,
    },
    cancelButtonText: {
        color: "#64748b",
        fontSize: 14,
        fontWeight: "500",
    },
    confirmButton: {
        backgroundColor: "#3b82f6",
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 6,
    },
    confirmButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "500",
    },
    // Year picker styles
    pickerContainer: {
        flex: 1,
        width: "100%",
    },
    pickerHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
        paddingBottom: 8,
        borderBottomWidth: 1,
        borderBottomColor: "#f1f5f9",
    },
    pickerTitle: {
        fontSize: 18,
        fontWeight: "600",
        color: "#0f172a",
    },
    closePickerButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: "#f1f5f9",
    },
    yearList: {
        maxHeight: 300,
    },
    yearGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
        paddingBottom: 16,
    },
    yearItem: {
        width: 80,
        margin: 4,
        padding: 12,
        borderRadius: 8,
        backgroundColor: "#f1f5f9",
        alignItems: "center",
        justifyContent: "center",
    },
    selectedYearItem: {
        backgroundColor: "#3b82f6",
    },
    yearText: {
        fontSize: 16,
        fontWeight: "500",
        color: "#0f172a",
    },
    selectedYearText: {
        color: "white",
        fontWeight: "600",
    },
    // Month picker styles
    monthGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
    },
    monthItem: {
        width: "30%",
        margin: "1.5%",
        padding: 12,
        borderRadius: 8,
        backgroundColor: "#f1f5f9",
        alignItems: "center",
        justifyContent: "center",
    },
    selectedMonthItem: {
        backgroundColor: "#3b82f6",
    },
    monthText: {
        fontSize: 14,
        fontWeight: "500",
        color: "#0f172a",
    },
    selectedMonthText: {
        color: "white",
        fontWeight: "600",
    },
});
