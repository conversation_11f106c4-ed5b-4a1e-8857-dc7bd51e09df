import { Share as ShareIcon } from "@/constants/icons";
import * as FileSystem from "expo-file-system";
import * as Print from "expo-print";
import * as Sharing from "expo-sharing";
import React from "react";
import {
  Alert,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface TicketDownloadProps {
    booking: any;
    onError?: (message: string) => void;
}

export default function TicketDownload({
    booking,
    onError,
}: TicketDownloadProps) {
    // Generate HTML for the ticket
    const generateTicketHTML = () => {
        if (!booking) return "";

        const formatDate = (dateString: string) => {
            if (!dateString) return "";
            return new Date(dateString).toLocaleDateString(undefined, {
                year: "numeric",
                month: "short",
                day: "numeric",
            });
        };

        const formatTime = (timeString: string) => {
            if (!timeString) return "";
            return timeString;
        };

        // Generate QR code URL for check-in
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://somexpress.com/checkin/${booking.PNR}`;

        return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>E-Ticket: ${booking.bookingNumber}</title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        * {
          margin: 0;
          padding: 0;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          background: #f8fafc;
          color: #1e293b;
          line-height: 1.5;
          -webkit-font-smoothing: antialiased;
        }
        
        .ticket-wrapper {
          max-width: 800px;
          margin: 32px auto;
          padding: 0 16px;
        }
        
        .ticket-container {r
          background: white;
          border-radius: 16px;
          overflow: hidden;
        }
        
        /* Header */
        .header {
          background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
          padding: 32px;
          color: white;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 24px;
        }
        
        .header-left {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 16px;
        }
        
        .logo {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          overflow: hidden;
          background: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }
        
        .logo img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        
        .airline-info {
          display: flex;
          flex-direction: column;
        }
        
        .airline-name {
          font-size: 14px;
          font-weight: 700;
          margin-bottom: 4px;
          letter-spacing: -0.025em;
        }
        
        .ticket-title {
          font-size: 12px;
          opacity: 0.8;
          font-weight: 500;
          letter-spacing: 0.05em;
        }
        
        .header-right {
          text-align: center;
        }
        
        .qr-section {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 12px;
          padding: 16px;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .qr-code img {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background: white;
          padding: 4px;
        }
        
        .pnr-code {
          font-size: 12px;
          font-weight: 700;
          margin-top: 12px;
          letter-spacing: 0.1em;
        }
        
        .qr-label {
          font-size: 12px;
          opacity: 0.8;
          margin-top: 4px;
        }
        
        /* Status Bar */
        .status-bar {
          background: #f1f5f9;
          padding: 20px 32px;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 24px;
        }
        
        .status-item {
          text-align: center;
        }
        
        .status-label {
          font-size: 12px;
          color: #64748b;
          font-weight: 500;
          margin-bottom: 4px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .status-value {
          font-size: 16px;
          font-weight: 600;
          color: #0f172a;
        }
        
        .status-confirmed {
          color: #059669;
        }
        
        /* Flight Section */
        .flight-section {
          padding: 32px;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .section-title {
          font-size: 20px;
          font-weight: 700;
          color: #0f172a;
          margin-bottom: 24px;
        }
        
        .flight-card {
          background: #f8fafc;
          border-radius: 12px;
          padding: 24px;
          margin-bottom: 16px;
          border: 1px solid #e2e8f0;
        }
        
        .flight-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }
        
        .flight-number {
          font-size: 18px;
          font-weight: 700;
          color: #1e40af;
        }
        
        .flight-badge {
          background: #dbeafe;
          color: #1e40af;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .route-display {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          position: relative;
        }
        
        .route-line {
          position: absolute;
          top: 50%;
          left: 25%;
          right: 25%;
          height: 2px;
          background: linear-gradient(to right, #e2e8f0, #cbd5e1, #e2e8f0);
          z-index: 1;
        }
        
        .airport {
          text-align: center;
          z-index: 2;
          background: #f8fafc;
          padding: 0 12px;
        }
        
        .airport-code {
          font-size: 24px;
          font-weight: 800;
          color: #0f172a;
          margin-bottom: 4px;
        }
        
        .airport-name {
          font-size: 13px;
          color: #64748b;
          font-weight: 500;
        }
        
        .plane-icon {
          background: #3b82f6;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          z-index: 3;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .flight-times {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #e2e8f0;
        }
        
        .time-group {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .time-icon {
          color: #64748b;
          font-size: 14px;
        }
        
        .time-text {
          font-size: 14px;
          color: #475569;
          font-weight: 500;
        }
        
        /* Passenger Section */
        .passenger-section {
          padding: 32px;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .passenger-card {
          background: #f8fafc;
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 12px;
          border: 1px solid #e2e8f0;
        }
        
        .passenger-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }
        
        .passenger-name {
          font-size: 18px;
          font-weight: 700;
          color: #0f172a;
        }
        
        .passenger-type {
          background: #e2e8f0;
          color: #475569;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
        
        .passenger-details {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }
        
        .detail-item {
          display: flex;
          flex-direction: column;
        }
        
        .detail-label {
          font-size: 12px;
          color: #64748b;
          font-weight: 500;
          margin-bottom: 4px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .detail-value {
          font-size: 14px;
          color: #0f172a;
          font-weight: 600;
        }
        
        /* Payment Section */
        .payment-section {
          padding: 32px;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .payment-card {
          background: #f8fafc;
          border-radius: 12px;
          padding: 24px;
          border: 1px solid #e2e8f0;
        }
        
        .payment-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
        }
        
        .payment-row:not(:last-child) {
          border-bottom: 1px solid #e2e8f0;
        }
        
        .payment-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
        
        .payment-value {
          font-size: 14px;
          color: #0f172a;
          font-weight: 600;
        }
        
        .payment-total {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 2px solid #e2e8f0;
        }
        
        .total-label {
          font-size: 16px;
          font-weight: 700;
          color: #0f172a;
        }
        
        .total-value {
          font-size: 18px;
          font-weight: 800;
          color: #1e40af;
        }
        
        /* Info Section */
        .info-section {
          padding: 32px;
          background: #f8fafc;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 32px;
        }
        
        .info-card {
          background: white;
          border-radius: 12px;
          padding: 24px;
          border: 1px solid #e2e8f0;
        }
        
        .info-title {
          font-size: 16px;
          font-weight: 700;
          color: #0f172a;
          margin-bottom: 16px;
        }
        
        .info-list {
          list-style: none;
          space-y: 8px;
        }
        
        .info-item {
          font-size: 14px;
          color: #475569;
          margin-bottom: 8px;
          padding-left: 20px;
          position: relative;
        }
        
        .info-item::before {
          content: '•';
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        
        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
        
        .contact-item {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 14px;
          color: #475569;
          font-weight: 500;
        }
        
        .contact-icon {
          color: #3b82f6;
          font-size: 16px;
        }
        
        /* Footer */
        .footer {
          text-align: center;
          padding: 32px;
          background: #0f172a;
          color: white;
        }
        
        .footer-title {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 8px;
        }
        
        .footer-subtitle {
          font-size: 14px;
          opacity: 0.7;
          margin-bottom: 16px;
        }
        
        .generated-info {
          font-size: 12px;
          opacity: 0.5;
          padding-top: 16px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
    
      </style>
    </head>
    <body>
      <div class="ticket-wrapper">
        <div class="ticket-container">
          <!-- Header -->
          <div class="header">
            <div class="header-left">
              <div class="logo">
                <img src="https://somexpressairways.com/assets/logo_small-DhmcQ3fF.png" alt="Logo" />
              </div>
              <div class="airline-info">
                <div class="airline-name">Somexpress Airways</div>
                <div class="ticket-title">ELECTRONIC TICKET</div>
              </div>
            </div>

            <div class="header-right">
              <div class="qr-section">
                <img src="${qrCodeUrl}" alt="Check-in QR Code" class="qr-code" 
                width="40" height="40"
                />
                <div class="pnr-code">${booking.PNR}</div>
                <div class="qr-label">Scan to check in</div>
              </div>
            </div>
          </div>
          
          <!-- Status Bar -->
          <div class="status-bar">
            <div class="status-item">
              <div class="status-label">Booking Number</div>
              <div class="status-value">${booking.bookingNumber}</div>
            </div>
            <div class="status-item">
              <div class="status-label">Trip Type</div>
              <div class="status-value">${
                  booking.tripType === "ROUND_TRIP"
                      ? "Round Trip"
                      : booking.tripType === "ONE_WAY"
                      ? "One Way"
                      : booking.tripType
              }</div>
            </div>
            <div class="status-item">
              <div class="status-label">Status</div>
              <div class="status-value status-confirmed">${booking.status.toUpperCase()}</div>
            </div>
          </div>
          
          <!-- Flight Details -->
          <div class="flight-section">
            <div class="section-title">Flight Details</div>
            
            ${booking.flightSegments
                .map(
                    (segment: any, index: number) => `
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-number">${
                      segment.flight.flightNumber
                  }</div>
                  <div class="flight-badge">${
                      index === 0 ? "Outbound" : "Return"
                  }</div>
                </div>
                
                <div class="route-display">
                  <div class="airport">
                    <div class="airport-code">${
                        segment.from.split(", ")[1] ||
                        segment.from.substring(0, 3).toUpperCase()
                    }</div>
                    <div class="airport-name">${
                        segment.from.split(", ")[0] || segment.from
                    }</div>
                  </div>
                  
                  <div class="plane-icon">✈</div>
                  
                  <div class="airport">
                    <div class="airport-code">${
                        segment.to.split(", ")[1] ||
                        segment.to.substring(0, 3).toUpperCase()
                    }</div>
                    <div class="airport-name">${
                        segment.to.split(", ")[0] || segment.to
                    }</div>
                  </div>
                  
                  <div class="route-line"></div>
                </div>
                
                <div class="flight-times">
                  <div class="time-group">
                    <span class="time-icon">📅</span>
                    <span class="time-text">Departure: ${formatDate(
                        segment.departureDate
                    )} ${formatTime(segment.departureTime || "")}</span>
                  </div>
                  ${
                      segment.arrivalDate
                          ? `
                  <div class="time-group">
                    <span class="time-icon">📅</span>
                    <span class="time-text">Arrival: ${formatDate(
                        segment.arrivalDate
                    )} ${formatTime(segment.arrivalTime || "")}</span>
                  </div>
                  `
                          : ""
                  }
                </div>
              </div>
            `
                )
                .join("")}
          </div>
          
          <!-- Passenger Information -->
          <div class="passenger-section">
            <div class="section-title">Passenger Information</div>
            
            ${booking.passengers
                .map(
                    (passenger: any) => `
              <div class="passenger-card">
                <div class="passenger-header">
                  <div class="passenger-name">${passenger.firstName} ${
                        passenger.lastName
                    }</div>
                  <div class="passenger-type">${passenger.passengerType}</div>
                </div>
                
                <div class="passenger-details">
                  ${
                      passenger.dateOfBirth
                          ? `
                    <div class="detail-item">
                      <div class="detail-label">Date of Birth</div>
                      <div class="detail-value">${formatDate(
                          passenger.dateOfBirth
                      )}</div>
                    </div>
                  `
                          : ""
                  }
                  
                  ${
                      passenger.passportInfo?.passportNumber
                          ? `
                    <div class="detail-item">
                      <div class="detail-label">Passport Number</div>
                      <div class="detail-value">${passenger.passportInfo.passportNumber}</div>
                    </div>
                  `
                          : ""
                  }
                  
                  ${
                      passenger.passportInfo?.country
                          ? `
                    <div class="detail-item">
                      <div class="detail-label">Nationality</div>
                      <div class="detail-value">${passenger.passportInfo.country}</div>
                    </div>
                  `
                          : ""
                  }
                  
                  ${
                      passenger.email
                          ? `
                    <div class="detail-item">
                      <div class="detail-label">Email</div>
                      <div class="detail-value">${passenger.email}</div>
                    </div>
                  `
                          : ""
                  }
                  
                  ${
                      passenger.seatAssignments &&
                      passenger.seatAssignments.length > 0
                          ? `
                    <div class="detail-item">
                      <div class="detail-label">Seat Assignment</div>
                      <div class="detail-value">
                        ${passenger.seatAssignments
                            .map((seat: any) => {
                                const flightNumber =
                                    booking.flightSegments.find(
                                        (s: any) => s._id === seat.segmentId
                                    )?.flight.flightNumber || "Flight";
                                return `${flightNumber}: ${seat.seatNumber}`;
                            })
                            .join(", ")}
                      </div>
                    </div>
                  `
                          : ""
                  }
                </div>
              </div>
            `
                )
                .join("")}
          </div>
          
          <!-- Payment Details -->
          ${
              booking.payment
                  ? `
          <div class="payment-section">
            <div class="section-title">Payment Summary</div>
            <div class="payment-card">
              ${
                  booking.payment.subTotal
                      ? `
              <div class="payment-row">
                <div class="payment-label">Subtotal</div>
                <div class="payment-value">$${booking.payment.subTotal.toFixed(
                    2
                )}</div>
              </div>
              `
                      : ""
              }
              ${
                  booking.payment.taxAmount
                      ? `
              <div class="payment-row">
                <div class="payment-label">Taxes & Fees</div>
                <div class="payment-value">$${booking.payment.taxAmount.toFixed(
                    2
                )}</div>
              </div>
              `
                      : ""
              }
              <div class="payment-row payment-total">
                <div class="total-label">Total Amount</div>
                <div class="total-value">$${(
                    Number(booking.payment.subTotal) +
                    Number(booking.payment.taxAmount)
                ).toFixed(2)}</div>
              </div>
              <div class="payment-row">
                <div class="payment-label">Payment Status</div>
                <div class="payment-value status-confirmed">${
                    booking.payment.paymentStatus?.toUpperCase() || "CONFIRMED"
                }</div>
              </div>
            </div>
          </div>
          `
                  : ""
          }
          
          <!-- Important Information -->
          <div class="info-section">
            <div class="info-grid">
              <div class="info-card">
                <div class="info-title">Travel Guidelines</div>
                <ul class="info-list">
                  <li class="info-item">Arrive 2 hours early for domestic flights</li>
                  <li class="info-item">Bring valid photo ID or passport</li>
                  <li class="info-item">Check-in closes 45-60 minutes before departure</li>
                  <li class="info-item">Contact us 24 hours before for changes</li>
                </ul>
              </div>
              
              <div class="info-card">
                <div class="info-title">Customer Support</div>
                <div class="contact-info">
                  <div class="contact-item">
                    <span class="contact-icon">📞</span>
                    <span>+252-619-222-773</span>
                  </div>
                  <div class="contact-item">
                    <span class="contact-icon">✉️</span>
                    <span><EMAIL></span>
                  </div>
                  <div class="contact-item">
                    <span class="contact-icon">🌐</span>
                    <span>www.somexpressairways.com</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            <div class="footer-title">Thank you for choosing Somexpress Airways</div>
            <div class="footer-subtitle">Your journey matters to us</div>
            <div class="generated-info">
              Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
    };

    const handleShareTicket = async () => {
        try {
            if (Platform.OS === "web") {
                // For web, open in a new tab
                const html = generateTicketHTML();
                const blob = new Blob([html], { type: "text/html" });
                const url = URL.createObjectURL(blob);
                window.open(url, "_blank");
            } else {
                // For mobile, create a temporary HTML file and share it
                const html = generateTicketHTML();
                const fileUri = `${FileSystem.cacheDirectory}ticket_${booking.bookingNumber}.html`;
                await FileSystem.writeAsStringAsync(fileUri, html);

                // Create PDF from HTML
                const pdfUri = await Print.printToFileAsync({
                    html,
                    base64: false,
                });

                // Share the PDF
                await Sharing.shareAsync(pdfUri.uri);
            }
        } catch (error) {
            console.error("Error sharing ticket:", error);
            onError && onError("Failed to share ticket");
            Alert.alert("Error", "Failed to share ticket");
        }
    };

    // Only show for confirmed bookings
    if (
        booking.status.toLowerCase() !== "confirmed" &&
        booking.status.toLowerCase() !== "booked"
    ) {
        return null;
    }

    return (
        <View style={styles.container}>
            <View style={styles.content}>
                <View style={styles.header}>
                    <View style={styles.textContainer}>
                        <Text style={styles.title}>E-Ticket Available</Text>
                        <Text style={styles.description}>
                            Share your boarding pass with others
                        </Text>
                    </View>
                    <TouchableOpacity
                        style={styles.shareButton}
                        onPress={handleShareTicket}
                    >
                        <ShareIcon size={20} color="#ffffff" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: "#f0f7ff",
        borderRadius: 12,
        marginBottom: 16,
        overflow: "hidden",

  },
    content: {
        padding: 16,
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    textContainer: {
        flex: 1,
    },
    title: {
        fontSize: 16,
        fontWeight: "600",
        color: "#1e40af",
        marginBottom: 4,
    },
    description: {
        fontSize: 14,
        color: "#3b82f6",
    },
    shareButton: {
        backgroundColor: "#4f46e5",
        width: 42,
        height: 42,
        borderRadius: 21,
        alignItems: "center",
        justifyContent: "center",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
});
