import api from "@/services/api";
import { create } from "zustand";

interface PaymentState {
    loading: boolean;
    error: string | null;
    success: boolean;

    // Payment methods
    paywithwallet: (data: any) => Promise<any>;
    payWithCash: (data: any) => Promise<any>;
    createReservation: (data: any) => Promise<any>;
    completeReservation: (data: any) => Promise<any>;
    completeReservationPayment: (data: any) => Promise<any>;

    // State management
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    setSuccess: (success: boolean) => void;
    resetState: () => void;
}

export const usePaymentStore = create<PaymentState>((set) => ({
    loading: false,
    error: null,
    success: false,

    // State management actions
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    setSuccess: (success) => set({ success }),
    resetState: () => set({ loading: false, error: null, success: false }),

    // Payment methods
    paywithwallet: async (data) => {
        set({ loading: true, error: null, success: false });
        try {
            const response = await api.post("/payment/wallet-payment", data);
            set({ loading: false, success: true });
            return response.data.data;
        } catch (error: any) {
            console.error("Wallet payment error:", error);
            set({
                loading: false,
                error:
                    error.response?.data?.message ||
                    "Failed to process wallet payment",
            });
            throw error.response?.data || error;
        }
    },

    payWithCash: async (data) => {
        set({ loading: true, error: null, success: false });
        try {
            const response = await api.post("/payment/cash-payment", data);
            set({ loading: false, success: true });
            return response.data.data;
        } catch (error: any) {
            console.error("Cash payment error:", error);
            set({
                loading: false,
                error:
                    error.response?.data?.message ||
                    "Failed to process cash payment",
            });
            throw error.response?.data || error;
        }
    },

    createReservation: async (data) => {
        set({ loading: true, error: null, success: false });
        try {
            const response = await api.post("/payment/reservation", data);
            set({ loading: false, success: true });
            return response.data.data;
        } catch (error: any) {
            console.error("Reservation creation error:", error);
            set({
                loading: false,
                error:
                    error.response?.data?.message ||
                    "Failed to create reservation",
            });
            throw error.response?.data || error;
        }
    },

    completeReservation: async (data) => {
        set({ loading: true, error: null, success: false });
        try {
            const response = await api.post(
                "/payment/reservation/complete",
                data
            );
            set({ loading: false, success: true });
            return response.data.data;
        } catch (error: any) {
            console.error("Reservation completion error:", error);
            set({
                loading: false,
                error:
                    error.response?.data?.message ||
                    "Failed to complete reservation",
            });
            throw error.response?.data || error;
        }
    },

    completeReservationPayment: async (data) => {
        set({ loading: true, error: null, success: false });
        try {
            const response = await api.post(
                "/payment/reservation/payment",
                data
            );
            set({ loading: false, success: true });
            return response.data.data;
        } catch (error: any) {
            console.error("Reservation payment error:", error);
            set({
                loading: false,
                error:
                    error.response?.data?.message ||
                    "Failed to process reservation payment",
            });
            throw error.response?.data || error;
        }
    },
}));
