export interface SeatConfig {
    class: string;
    numberOfSeats: number;
    layout: string;
}

export interface Seat {
    seatNumber: string;
    row: number;
    column: string;
    class: string;
    status: string;
    side: string;
}

export function generateSeats(seatConfig: SeatConfig[]) {
    const seats: Seat[] = [];
    let currentRowNumber = 1;

    seatConfig.forEach((classConfig) => {
        const { class: className, numberOfSeats, layout } = classConfig;
        if (!layout || !numberOfSeats) return;

        const [leftCount, rightCount] = layout.split("-").map(Number);
        // const seatsPerRow = leftCount + rightCount;
        let seatsCreated = 0;

        // Use a while loop for clarity
        while (seatsCreated < numberOfSeats) {
            // Generate left side seats
            for (
                let i = 0;
                i < leftCount && seatsCreated < numberOfSeats;
                i++
            ) {
                const column = String.fromCharCode("A".charCodeAt(0) + i);
                seats.push({
                    seatNumber: `${currentRowNumber}${column}`,
                    row: currentRowNumber,
                    column: column,
                    class: className,
                    status: "Available",
                    side: "Left",
                });
                seatsCreated++;
            }

            // Generate right side seats
            for (
                let i = 0;
                i < rightCount && seatsCreated < numberOfSeats;
                i++
            ) {
                const column = String.fromCharCode(
                    "A".charCodeAt(0) + leftCount + i
                );
                seats.push({
                    seatNumber: `${currentRowNumber}${column}`,
                    row: currentRowNumber,
                    column: column,
                    class: className,
                    status: "Available",
                    side: "Right",
                });
                seatsCreated++;
            }

            currentRowNumber++; // Always increment the row after finishing a row
        }
    });

    return seats;
}
